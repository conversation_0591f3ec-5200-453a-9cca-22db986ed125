#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 智能体系统完整测试

测试所有智能体的独立运行和包导入功能
"""

import sys
import os
import subprocess
import importlib

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_agent_independent_run(agent_path: str, test_args: list) -> bool:
    """测试智能体独立运行"""
    try:
        cmd = [
            "D:/ProgramData/Miniconda3/envs/llms/python.exe",
            agent_path
        ] + test_args
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.path.join(os.path.dirname(__file__), '..')
        )
        
        if result.returncode == 0:
            print(f"   ✓ 独立运行成功")
            return True
        else:
            print(f"   ✗ 独立运行失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"   ✗ 独立运行超时")
        return False
    except Exception as e:
        print(f"   ✗ 独立运行异常: {e}")
        return False

def test_agent_package_import(module_name: str, function_name: str) -> bool:
    """测试智能体包导入"""
    try:
        module = importlib.import_module(module_name)
        function = getattr(module, function_name)
        print(f"   ✓ 包导入成功: {module_name}.{function_name}")
        return True
    except ImportError as e:
        print(f"   ✗ 包导入失败: {e}")
        return False
    except AttributeError as e:
        print(f"   ✗ 函数不存在: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 包导入异常: {e}")
        return False

def test_main_py_import() -> bool:
    """测试main.py导入"""
    try:
        import main
        print("   ✓ main.py导入成功")
        
        # 检查智能体注册表
        if hasattr(main, 'AGENT_REGISTRY'):
            agent_count = len(main.AGENT_REGISTRY)
            print(f"   ✓ 发现 {agent_count} 个注册的智能体")
            
            for key, config in main.AGENT_REGISTRY.items():
                required_fields = ['name', 'description', 'module', 'function']
                missing_fields = [field for field in required_fields if field not in config]
                if missing_fields:
                    print(f"   ⚠ 智能体 '{key}' 缺少字段: {missing_fields}")
                else:
                    print(f"   ✓ 智能体 '{key}' 配置完整")
            
            return True
        else:
            print("   ✗ AGENT_REGISTRY 不存在")
            return False
            
    except Exception as e:
        print(f"   ✗ main.py导入失败: {e}")
        return False

def test_comp_analyzer():
    """测试竞品分析智能体"""
    print("\n" + "="*60)
    print("测试竞品分析智能体")
    print("="*60)
    
    results = []
    
    # 测试包导入
    print("1. 测试包导入...")
    result1 = test_agent_package_import(
        "agents.comp_analyzer.agent", 
        "run_competitor_analysis"
    )
    results.append(result1)
    
    # 测试独立运行
    print("2. 测试独立运行...")
    result2 = test_agent_independent_run(
        "agents/comp_analyzer/agent.py",
        ["--help"]
    )
    results.append(result2)
    
    return all(results)

def test_task_planner():
    """测试任务规划智能体"""
    print("\n" + "="*60)
    print("测试任务规划智能体")
    print("="*60)
    
    results = []
    
    # 测试包导入
    print("1. 测试包导入...")
    result1 = test_agent_package_import(
        "agents.task_planner", 
        "run_task_planner"
    )
    results.append(result1)
    
    # 测试独立运行
    print("2. 测试独立运行...")
    result2 = test_agent_independent_run(
        "agents/task_planner.py",
        ["--help"]
    )
    results.append(result2)
    
    return all(results)

def test_aica_agent():
    """测试AICA智能体"""
    print("\n" + "="*60)
    print("测试AICA智能体")
    print("="*60)
    
    results = []
    
    # 测试包导入
    print("1. 测试包导入...")
    result1 = test_agent_package_import(
        "agents.aica_agent.enhanced_graph", 
        "run_aica_analysis"
    )
    results.append(result1)
    
    return all(results)

def test_shared_modules():
    """测试共享模块"""
    print("\n" + "="*60)
    print("测试共享模块")
    print("="*60)
    
    results = []
    
    # 测试utils模块
    print("1. 测试utils模块...")
    modules_to_test = [
        ("agents.utils.logger", "get_logger"),
        ("agents.utils.chain_logger", "create_logging_chain"),
        ("agents.utils.llm_wrapper", "create_llm_with_timeout"),
    ]
    
    for module_name, function_name in modules_to_test:
        print(f"   测试 {module_name}.{function_name}...")
        result = test_agent_package_import(module_name, function_name)
        results.append(result)
    
    # 测试tools模块
    print("2. 测试tools模块...")
    try:
        from agents.tools import web_tools
        print("   ✓ tools.web_tools导入成功")
        results.append(True)
    except Exception as e:
        print(f"   ✗ tools.web_tools导入失败: {e}")
        results.append(False)
    
    return all(results)

def test_system_integration():
    """测试系统集成"""
    print("\n" + "="*60)
    print("测试系统集成")
    print("="*60)
    
    results = []
    
    # 测试main.py
    print("1. 测试main.py...")
    result1 = test_main_py_import()
    results.append(result1)
    
    # 测试智能体注册表完整性
    print("2. 测试智能体注册表...")
    try:
        import main
        
        for agent_key, agent_config in main.AGENT_REGISTRY.items():
            print(f"   验证智能体: {agent_key}")
            
            # 验证模块和函数存在
            module_name = agent_config.get('module')
            function_name = agent_config.get('function')
            
            if module_name and function_name:
                result = test_agent_package_import(module_name, function_name)
                if result:
                    print(f"   ✓ {agent_key} 验证通过")
                else:
                    print(f"   ✗ {agent_key} 验证失败")
                results.append(result)
            else:
                print(f"   ✗ {agent_key} 配置不完整")
                results.append(False)
        
    except Exception as e:
        print(f"   ✗ 智能体注册表测试失败: {e}")
        results.append(False)
    
    return all(results)

def main():
    """主测试函数"""
    print("AICA 智能体系统完整测试")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    test_functions = [
        ("共享模块测试", test_shared_modules),
        ("竞品分析智能体测试", test_comp_analyzer),
        ("任务规划智能体测试", test_task_planner),
        ("AICA智能体测试", test_aica_agent),
        ("系统集成测试", test_system_integration),
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！AICA智能体系统运行正常")
        print("\n✅ 系统功能验证:")
        print("- ✓ 所有智能体可独立运行")
        print("- ✓ 所有智能体可作为包导入")
        print("- ✓ 共享模块工作正常")
        print("- ✓ 主程序集成正常")
        print("- ✓ 智能体注册机制正常")
        
        print("\n🚀 可以开始使用系统:")
        print("   python main.py")
        
        return True
    else:
        print(f"\n❌ {len(results) - passed} 个测试失败，需要修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断")
        sys.exit(0)
