# aica_agent/utils/logger.py
import logging
import os
import sys
from datetime import datetime
from typing import Optional
import colorama
from colorama import Fore, Back, Style

# 初始化colorama
colorama.init(autoreset=True)

class ColoredFormatter(logging.Formatter):
    """自定义彩色日志格式化器"""
    
    # 定义不同日志级别的颜色
    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Back.WHITE,
        'PLANNING': Fore.BLUE + Style.BRIGHT,
        'EXECUTION': Fore.MAGENTA + Style.BRIGHT,
        'REFLECTION': Fore.YELLOW + Style.BRIGHT,
        'SEARCH': Fore.CYAN + Style.BRIGHT,
        'SCRAPE': Fore.WHITE + Style.BRIGHT,
        'RESULT': Fore.GREEN + Style.BRIGHT,
    }
    
    def format(self, record):
        # 获取颜色
        color = self.COLORS.get(record.levelname, '')
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S.%f')[:-3]
        
        # 构建彩色日志消息
        if hasattr(record, 'phase'):
            phase_color = self.COLORS.get(record.phase.upper(), Fore.WHITE)
            colored_msg = f"{color}[{timestamp}] [{record.levelname}] {phase_color}[{record.phase}]{Style.RESET_ALL} {record.getMessage()}"
        else:
            colored_msg = f"{color}[{timestamp}] [{record.levelname}]{Style.RESET_ALL} {record.getMessage()}"
        
        return colored_msg

class AICALogger:
    """AICA项目专用日志器"""
    
    def __init__(self, name: str = "AICA", log_dir: str = "logs"):
        self.name = name
        self.log_dir = log_dir
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 生成唯一的日志文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(log_dir, f"aica_run_{timestamp}.log")
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 设置控制台处理器（彩色输出）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = ColoredFormatter()
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 设置文件处理器（纯文本输出）
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 记录启动信息
        self.info("=" * 80)
        self.info(f"AICA Agent 启动 - 日志文件: {self.log_file}")
        self.info("=" * 80)
    
    def _log_with_phase(self, level: str, phase: str, message: str, **kwargs):
        """带阶段标识的日志记录"""
        extra = {'phase': phase}
        getattr(self.logger, level.lower())(message, extra=extra, **kwargs)
    
    def debug(self, message: str, phase: Optional[str] = None):
        if phase:
            self._log_with_phase('DEBUG', phase, message)
        else:
            self.logger.debug(message)
    
    def info(self, message: str, phase: Optional[str] = None):
        if phase:
            self._log_with_phase('INFO', phase, message)
        else:
            self.logger.info(message)
    
    def warning(self, message: str, phase: Optional[str] = None):
        if phase:
            self._log_with_phase('WARNING', phase, message)
        else:
            self.logger.warning(message)
    
    def error(self, message: str, phase: Optional[str] = None):
        if phase:
            self._log_with_phase('ERROR', phase, message)
        else:
            self.logger.error(message)
    
    def critical(self, message: str, phase: Optional[str] = None):
        if phase:
            self._log_with_phase('CRITICAL', phase, message)
        else:
            self.logger.critical(message)
    
    # 专用方法用于不同阶段
    def planning(self, message: str):
        """规划阶段日志"""
        self._log_with_phase('INFO', 'PLANNING', message)
    
    def execution(self, message: str):
        """执行阶段日志"""
        self._log_with_phase('INFO', 'EXECUTION', message)
    
    def reflection(self, message: str):
        """反思阶段日志"""
        self._log_with_phase('INFO', 'REFLECTION', message)
    
    def search(self, message: str):
        """搜索阶段日志"""
        self._log_with_phase('INFO', 'SEARCH', message)
    
    def scrape(self, message: str):
        """抓取阶段日志"""
        self._log_with_phase('INFO', 'SCRAPE', message)
    
    def result(self, message: str):
        """结果阶段日志"""
        self._log_with_phase('INFO', 'RESULT', message)
    
    def log_input_output(self, phase: str, input_data: any, output_data: any):
        """记录输入输出数据"""
        self.debug(f"输入数据: {input_data}", phase)
        self.debug(f"输出数据: {output_data}", phase)
    
    def log_node_execution(self, node_name: str, input_state: dict, output_state: dict):
        """记录节点执行的输入输出状态"""
        self.info(f"节点 '{node_name}' 开始执行")
        self.debug(f"输入状态: {self._format_state(input_state)}")
        self.debug(f"输出状态: {self._format_state(output_state)}")
        self.info(f"节点 '{node_name}' 执行完成")
    
    def _format_state(self, state: dict) -> str:
        """格式化状态信息，避免过长的输出"""
        if not state:
            return "空状态"
        
        formatted = {}
        for key, value in state.items():
            if isinstance(value, str) and len(value) > 100:
                formatted[key] = f"{value[:100]}..."
            elif hasattr(value, '__iter__') and not isinstance(value, str):
                try:
                    formatted[key] = f"[列表/迭代器，长度: {len(list(value))}]"
                except:
                    formatted[key] = "[迭代器对象]"
            else:
                formatted[key] = value
        
        return str(formatted)

# 全局日志器实例
logger = None

def get_logger() -> AICALogger:
    """获取全局日志器实例"""
    global logger
    if logger is None:
        logger = AICALogger()
    return logger

def init_logger(log_dir: str = "logs") -> AICALogger:
    """初始化日志器"""
    global logger
    logger = AICALogger(log_dir=log_dir)
    return logger
