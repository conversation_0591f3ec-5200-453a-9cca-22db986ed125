#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 增强版系统测试脚本
测试新增的竞品确认和详细研究功能
"""

import os
import sys
from datetime import datetime

def test_enhanced_imports():
    """测试增强版模块导入"""
    print("🔍 测试增强版模块导入...")
    
    try:
        from aica_agent.enhanced_nodes import competitor_confirmation_node, detailed_research_node
        print("✅ 增强版节点导入成功")
    except Exception as e:
        print(f"❌ 增强版节点导入失败: {e}")
        return False
    
    try:
        from aica_agent.enhanced_graph import build_enhanced_graph, build_traditional_graph
        print("✅ 增强版图导入成功")
    except Exception as e:
        print(f"❌ 增强版图导入失败: {e}")
        return False
    
    try:
        from aica_agent.chains import (
            competitor_identification_chain,
            competitor_selection_chain,
            keyword_generation_chain,
            content_relevance_chain,
            content_analysis_chain
        )
        print("✅ 增强版链导入成功")
    except Exception as e:
        print(f"❌ 增强版链导入失败: {e}")
        return False
    
    try:
        from aica_agent.utils.callbacks import AICACallbackHandler, PhaseCallbackManager
        print("✅ Callback系统导入成功")
    except Exception as e:
        print(f"❌ Callback系统导入失败: {e}")
        return False
    
    return True

def test_enhanced_state():
    """测试增强版状态管理"""
    print("\n🔍 测试增强版状态管理...")
    
    try:
        from aica_agent.state import (
            AgentState, CompetitorInfo, SearchResult, 
            CompetitorResearch, DetailedContent
        )
        
        # 创建测试数据
        competitor_info = CompetitorInfo(
            name="测试竞品",
            company="测试公司",
            main_functions=["功能1", "功能2"],
            description="测试描述",
            relevance_score=0.9
        )
        
        search_result = SearchResult(
            title="测试标题",
            url="https://test.com",
            snippet="测试摘要",
            relevance_score=0.8
        )
        
        detailed_content = DetailedContent(
            url="https://test.com",
            title="测试标题",
            content="测试内容",
            keywords=["关键词1", "关键词2"],
            summary="测试摘要"
        )
        
        competitor_research = CompetitorResearch(
            competitor_info=competitor_info,
            search_keywords=["关键词1", "关键词2"],
            collected_contents=[detailed_content],
            analysis_summary="测试分析总结",
            is_complete=True
        )
        
        # 创建增强版状态
        state = AgentState(
            initial_input="测试输入",
            initial_search_results=[search_result],
            identified_competitors=[competitor_info],
            selected_competitors=[competitor_info],
            competitor_research=[competitor_research]
        )
        
        print(f"✅ 增强版状态管理测试成功")
        print(f"   - 搜索结果数: {len(state.initial_search_results)}")
        print(f"   - 识别竞品数: {len(state.identified_competitors)}")
        print(f"   - 选定竞品数: {len(state.selected_competitors)}")
        print(f"   - 研究结果数: {len(state.competitor_research)}")
        return True
    except Exception as e:
        print(f"❌ 增强版状态管理测试失败: {e}")
        return False

def test_config_parameters():
    """测试配置参数"""
    print("\n🔍 测试配置参数...")
    
    try:
        from config import (
            INITIAL_SEARCH_RESULTS,
            SELECTED_COMPETITORS,
            KEYWORDS_PER_COMPETITOR,
            MAX_PAGES_PER_KEYWORD,
            CONTENT_ANALYSIS_THRESHOLD
        )
        
        print(f"✅ 配置参数加载成功")
        print(f"   - 初始搜索结果数: {INITIAL_SEARCH_RESULTS}")
        print(f"   - 选定竞品数: {SELECTED_COMPETITORS}")
        print(f"   - 每竞品关键词数: {KEYWORDS_PER_COMPETITOR}")
        print(f"   - 每关键词最大页面数: {MAX_PAGES_PER_KEYWORD}")
        print(f"   - 内容分析阈值: {CONTENT_ANALYSIS_THRESHOLD}")
        return True
    except Exception as e:
        print(f"❌ 配置参数测试失败: {e}")
        return False

def test_callback_system():
    """测试Callback系统"""
    print("\n🔍 测试Callback系统...")
    
    try:
        from aica_agent.utils.callbacks import (
            AICACallbackHandler, 
            PhaseCallbackManager,
            create_callback_handler,
            get_phase_callbacks
        )
        from aica_agent.utils.logger import init_logger
        
        # 初始化日志系统
        logger = init_logger("test_logs")
        
        # 创建callback处理器
        callback_handler = create_callback_handler("TEST")
        
        # 测试不同阶段的callbacks
        planning_callbacks = PhaseCallbackManager.get_planning_callbacks()
        confirmation_callbacks = PhaseCallbackManager.get_competitor_confirmation_callbacks()
        research_callbacks = PhaseCallbackManager.get_detailed_research_callbacks()
        
        print(f"✅ Callback系统测试成功")
        print(f"   - 规划阶段callbacks: {len(planning_callbacks)}")
        print(f"   - 竞品确认callbacks: {len(confirmation_callbacks)}")
        print(f"   - 详细研究callbacks: {len(research_callbacks)}")
        return True
    except Exception as e:
        print(f"❌ Callback系统测试失败: {e}")
        return False

def test_enhanced_chains():
    """测试增强版链（不调用LLM）"""
    print("\n🔍 测试增强版链结构...")
    
    try:
        from aica_agent.chains import (
            competitor_identification_chain,
            competitor_selection_chain,
            keyword_generation_chain,
            content_relevance_chain,
            content_analysis_chain
        )
        
        # 检查链是否正确构建
        chains = [
            ("竞品识别链", competitor_identification_chain),
            ("竞品选择链", competitor_selection_chain),
            ("关键词生成链", keyword_generation_chain),
            ("内容相关性链", content_relevance_chain),
            ("内容分析链", content_analysis_chain)
        ]
        
        for name, chain in chains:
            if chain is not None:
                print(f"✅ {name} 构建成功")
            else:
                print(f"❌ {name} 构建失败")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 增强版链测试失败: {e}")
        return False

def test_graph_building():
    """测试图构建"""
    print("\n🔍 测试图构建...")
    
    try:
        from aica_agent.enhanced_graph import build_enhanced_graph, build_traditional_graph
        
        # 测试增强版图构建
        enhanced_app = build_enhanced_graph()
        if enhanced_app is not None:
            print("✅ 增强版图构建成功")
        else:
            print("❌ 增强版图构建失败")
            return False
        
        # 测试传统版图构建
        traditional_app = build_traditional_graph()
        if traditional_app is not None:
            print("✅ 传统版图构建成功")
        else:
            print("❌ 传统版图构建失败")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 图构建测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 测试文件结构...")
    
    required_files = [
        "aica_agent/enhanced_nodes.py",
        "aica_agent/enhanced_graph.py",
        "aica_agent/utils/callbacks.py",
        "docs/",
        "logs/"
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 AICA 增强版系统测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("增强版模块导入", test_enhanced_imports()))
    test_results.append(("文件结构", test_file_structure()))
    test_results.append(("配置参数", test_config_parameters()))
    test_results.append(("增强版状态管理", test_enhanced_state()))
    test_results.append(("Callback系统", test_callback_system()))
    test_results.append(("增强版链结构", test_enhanced_chains()))
    test_results.append(("图构建", test_graph_building()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 增强版测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有增强版测试通过！系统准备就绪。")
        print("\n📝 下一步:")
        print("1. 安装网络依赖: pip install ddgs langgraph langchain-community")
        print("2. 运行增强版系统: python main.py")
        print("3. 选择增强模式进行测试")
        return True
    else:
        print("⚠️  部分增强版测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
