#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试修复后的chain系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_competitor_identification_chain():
    """测试竞品识别chain"""
    print("测试竞品识别chain...")
    
    try:
        from aica_agent.chains import competitor_identification_chain
        
        # 测试输入
        test_input = {
            "query": "中医辅助诊疗系统",
            "search_results": """1. 标题: 大经中医智能辅助诊疗系统
   链接: http://dajingtcm.com/product
   摘要: 产品解决方案 中医智能辅助诊疗系统

2. 标题: 问止中医
   链接: https://techtcm.com/
   摘要: 中医大脑 中医人工智能"""
        }
        
        print("开始执行竞品识别chain...")
        result = competitor_identification_chain.invoke(test_input)
        
        print(f"执行成功！结果类型: {type(result)}")
        if hasattr(result, 'dict'):
            result_dict = result.dict()
            print(f"识别的竞品数量: {len(result_dict.get('competitors', []))}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_planning_chain():
    """测试规划chain"""
    print("\n测试规划chain...")
    
    try:
        from aica_agent.chains import planning_chain
        
        # 测试输入
        test_input = {
            "initial_input": "中医辅助诊疗系统"
        }
        
        print("开始执行规划chain...")
        result = planning_chain.invoke(test_input)
        
        print(f"执行成功！结果类型: {type(result)}")
        if hasattr(result, 'dict'):
            result_dict = result.dict()
            print(f"竞品数量: {len(result_dict.get('competitors_to_research', []))}")
            print(f"子任务数量: {len(result_dict.get('sub_tasks', []))}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("简单Chain测试")
    print("=" * 60)
    
    tests = [
        ("竞品识别Chain", test_competitor_identification_chain),
        ("规划Chain", test_planning_chain),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Chain系统工作正常！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
