# aica_agent/models.py
"""
定义项目所使用的所有Pydantic数据模型和LangGraph的状态TypedDict。
"""
from typing import List, Optional, Dict, TypedDict
from pydantic import BaseModel, Field

class RadarChartData(BaseModel):
    """为雷达图提供结构化数据"""
    attributes: List[str] = Field(..., description="雷达图的维度/属性列表，例如 '功能丰富度', '易用性', '价格优势', '品牌声誉', '创新能力'。")
    scores: Dict[str, List[float]] = Field(..., description="各产品在这些维度上的评分 (0-10)。键是产品名，值是对应维度的评分列表。")

class CompetitorProfile(BaseModel):
    """存储单个竞品的深度分析信息"""
    name: str
    company_overview: str = Field(..., description="公司背景、规模和市场地位的简要介绍。")
    key_features: List[str] = Field(..., description="产品的核心功能点。")
    target_audience_profile: str = Field(..., description="详细的用户画像描述，包括人口统计学特征、痛点和需求。")
    pricing_model: str = Field(..., description="详细的定价模型描述，包括不同层级的价格和功能。")
    value_proposition: str = Field(..., description="独特价值主张和市场定位。")
    market_perception: str = Field(..., description="市场声誉和用户评价总结。")

class AnalysisReport(BaseModel):
    """最终输出的专业级竞品分析报告结构"""
    executive_summary: str
    our_product_profile: Dict[str, str] = Field(..., description="我们产品的档案，包括摘要和价值主张。")
    macro_environment_analysis: Dict[str, Dict[str, List[str]]] = Field(..., description="宏观环境分析，包含PEST和波特五力模型。")
    swot_analysis: Dict[str, List[str]]
    competitor_profiles: List[CompetitorProfile]
    comparative_analysis_tables: Dict[str, Dict] = Field(..., description="包含多个对比表的字典，例如功能对比矩阵。")
    radar_chart_data: RadarChartData
    strategic_recommendations: List[str]

class CompetitorAnalysisState(BaseModel):
    """智能体在图的节点之间传递的状态"""
    product_name: str
    competitor_count: int
    known_competitors: List[str]
    focus_features: List[str]
    
    our_product_profile: Optional[Dict[str, str]] = None
    macro_analysis: Optional[Dict] = None
    swot_analysis: Optional[Dict] = None
    
    competitor_list: List[str] = []
    research_data: Dict[str, CompetitorProfile] = {}
    competitor_to_analyze: Optional[str] = None
    
    final_report: Optional[AnalysisReport] = None