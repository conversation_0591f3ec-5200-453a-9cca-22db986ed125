"""
任务规划智能体 (Task Planner Agent)

该模块既可以作为一个独立的命令行工具运行，也可以被其他Python脚本导入调用。
它接收一个高层次的目标，并输出一个结构化的、包含任务列表和验收标准的JSON计划。
"""

import os
import json
import argparse
import sys
from typing import List, Optional, TypedDict

# Pydantic 用于定义和验证数据结构
from pydantic import BaseModel, Field

# LangChain 和 LangGraph 的核心组件
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langgraph.graph import END, StateGraph

# 处理导入路径，支持独立运行和包导入
def setup_imports():
    """设置导入路径，支持独立运行和包导入"""
    if __name__ == "__main__":
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

setup_imports()

# 导入日志系统 - 支持两种导入方式
try:
    # 尝试相对导入（包导入时）
    from .utils.logger import get_logger
except ImportError:
    # 回退到绝对导入（独立运行时）
    from agents.utils.logger import get_logger

# --- 1. 全局配置与常量 ---

# 初始化日志系统
logger = get_logger()

# 全局verbose控制
VERBOSE = True  # 默认开启终端输出

def set_verbose(verbose: bool):
    """设置是否在终端显示详细信息"""
    global VERBOSE
    VERBOSE = verbose

def log_and_print(message: str, level: str = "info", phase: str = "TASK_PLANNER"):
    """统一的日志和打印函数"""
    # 日志中始终记录
    if level == "info":
        logger.info(f"[{phase}] {message}", phase)
    elif level == "warning":
        logger.warning(f"[{phase}] {message}", phase)
    elif level == "error":
        logger.error(f"[{phase}] {message}", phase)
    elif level == "debug":
        logger.debug(f"[{phase}] {message}", phase)

    # 根据verbose控制是否在终端显示
    if VERBOSE:
        print(f"[{phase}] {message}")

try:
    from dotenv import load_dotenv
    load_dotenv()
    log_and_print("环境变量已加载", "info")
except ImportError:
    log_and_print("python-dotenv 未安装，将从环境中直接读取API密钥", "warning")

# 控制智能体在放弃前的最大修订次数
MAX_REVISIONS = 3

# --- 2. Pydantic 模型：定义结构化输出 ---

class Task(BaseModel):
    """定义单个任务的结构"""
    task_id: int = Field(..., description="任务的唯一标识符，从1开始递增。")
    description: str = Field(..., description="对任务的清晰、简洁的描述。")
    dependencies: List[int] = Field(default_factory=list, description="该任务依赖的其他任务的task_id列表。")
    acceptance_criteria: List[str] = Field(..., description="一个字符串列表，其中每一项都是一个具体的、可验证的验收标准。")

class Plan(BaseModel):
    """定义完整的计划，它由一系列任务组成"""
    tasks: List[Task]


# --- 3. LangGraph 状态定义 ---

class TaskPlannerState(TypedDict):
    """智能体在图的节点之间传递的状态"""
    original_goal: str
    draft_plan: Optional[Plan]
    critique: Optional[str]
    research_needed: bool
    research_query: Optional[str]
    research_results: Optional[str]
    revision_count: int


# --- 4. 核心组件（模型和工具）初始化 ---

# 初始化将在所有节点中使用的语言模型
model = ChatOpenAI(
    model="deepseek-r1-0528",
    api_key=os.getenv("QWEN_API_KEY"),
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
    temperature=0
)
# 初始化搜索工具
search_tool = DuckDuckGoSearchRun(name="web_search")


# --- 5. LangGraph 节点定义 ---

def planner_node(state: TaskPlannerState) -> dict:
    # ... (此处代码与Notebook中的 Cell 6 完全相同)
    log_and_print("👩‍💻 进入规划师节点 (JSON模式)", "info", "PLANNER")
    parser = JsonOutputParser(pydantic_object=Plan)
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位顶级的系统架构师和项目经理，专门为AI自动化工作流设计任务计划。
            你的任务是根据用户的目标，创建一个结构化的、机器可读的任务列表。
            你必须严格遵循以下JSON格式，并且只输出JSON，不包含任何额外的解释或Markdown标记。

            {format_instructions}
         """),
        ("human", 
         """请为以下用户目标制定一个任务计划。
            用户目标: {original_goal}

            **关键要求**:
            1.  **任务分解**: 将目标分解为一系列具体、独立的任务。
            2.  **依赖关系**: 明确每个任务的前置依赖任务。根任务的依赖为空列表 `[]`。
            3.  **验收标准 (Acceptance Criteria)**: 为每个任务提供一组清晰、具体、可验证的验收标准。这是最重要的部分。验收标准必须是二元的（即可以明确判断为'完成'或'未完成'），而不是模糊的。例如，使用“代码通过所有单元测试”而不是“代码质量好”。

            可用的研究资料 (如果有的话):
            {research_results}
            """
        )
    ])
    chain = prompt_template | model | parser
    try:
        # 记录输入
        input_data = {
            "original_goal": state["original_goal"],
            "research_results": state.get("research_results"),
            "format_instructions": parser.get_format_instructions()
        }
        log_and_print(f"规划师输入: {json.dumps(input_data, ensure_ascii=False, indent=2)}", "debug", "PLANNER")

        plan = chain.invoke(input_data)

        # 记录输出
        log_and_print(f"规划师输出: {json.dumps(plan, ensure_ascii=False, indent=2)}", "debug", "PLANNER")
        log_and_print("规划师决策: 已成功生成结构化计划草案", "info", "PLANNER")

        return {"draft_plan": Plan.model_validate(plan), "research_needed": False}
    except Exception as e:
        log_and_print(f"规划师决策: 无法直接生成计划，需要先进行研究。错误: {e}", "warning", "PLANNER")
        research_prompt = ChatPromptTemplate.from_template("为实现目标 '{goal}'，生成一个合适的网络搜索查询语句。")
        research_chain = research_prompt | model
        query = research_chain.invoke({"goal": state["original_goal"]}).content.strip()
        log_and_print(f"生成研究查询: {query}", "info", "PLANNER")
        return {"research_needed": True, "research_query": query}


def researcher_node(state: TaskPlannerState) -> dict:
    log_and_print("🧑‍🔬 进入研究员节点", "info", "RESEARCHER")
    query = state["research_query"]

    # 记录输入
    log_and_print(f"研究员输入: {json.dumps({'research_query': query}, ensure_ascii=False, indent=2)}", "debug", "RESEARCHER")
    log_and_print(f"正在使用 DuckDuckGo 搜索: '{query}'", "info", "RESEARCHER")

    results = search_tool.run(query)

    # 记录输出
    log_and_print(f"研究员输出: {json.dumps({'research_results': results[:500] + '...' if len(results) > 500 else results}, ensure_ascii=False, indent=2)}", "debug", "RESEARCHER")
    log_and_print("研究完成", "info", "RESEARCHER")

    return {"research_results": results}


def critique_node(state: TaskPlannerState) -> dict:
    log_and_print("🧐 进入批评家节点 (JSON审查模式)", "info", "CRITIC")
    plan_json_str = state["draft_plan"].model_dump_json(indent=2)

    # 记录输入
    log_and_print(f"批评家输入: {json.dumps({'plan': plan_json_str}, ensure_ascii=False, indent=2)}", "debug", "CRITIC")
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位经验丰富的QA（质量保证）工程师，专门审查自动化任务计划。
            你的任务是审查以下JSON格式的任务计划，并找出其中的问题。"""),
        ("human",
         """请审查以下JSON计划：
            ```json
            {plan}
            ```

            **请重点检查以下方面**:
            1.  **逻辑漏洞**: 任务分解是否合理？是否存在明显的步骤遗漏？
            2.  **依赖关系**: `dependencies` 是否正确？是否存在循环依赖或错误的依赖关系？
            3.  **验收标准质量**: 这是最重要的！`acceptance_criteria` 是否具体、可衡量、可验证？它们是否足够清晰，可以让一个独立的AI智能体判断任务是否完成？
                - **坏标准 (Bad)**: "代码已优化", "文档已编写"
                - **好标准 (Good)**: "代码性能测试显示延迟低于100ms", "API文档已生成并部署到/docs路径", "所有代码都通过了 linter 检查且没有错误"

            **你的输出**:
            - 如果计划质量很高，没有任何问题，请只回答 `NO_ISSUES`。
            - 否则，以列表形式清晰地列出你需要提出的所有批评和改进建议。""")
    ])
    chain = prompt_template | model
    result = chain.invoke({"plan": plan_json_str})
    response_text = result.content.strip()

    # 记录输出
    log_and_print(f"批评家输出: {json.dumps({'critique_result': response_text}, ensure_ascii=False, indent=2)}", "debug", "CRITIC")

    if "NO_ISSUES" in response_text:
        log_and_print("批评家决策: 计划质量达标", "info", "CRITIC")
        return {"critique": None}
    else:
        log_and_print("批评家决策: 计划存在缺陷，需要修订", "warning", "CRITIC")
        return {"critique": response_text}


def reviser_node(state: TaskPlannerState) -> dict:
    # ... (此处代码与Notebook中的 Cell 9 完全相同)
    log_and_print("📝 进入修订者节点 (JSON修订模式)", "info", "REVISER")
    parser = JsonOutputParser(pydantic_object=Plan)
    plan_json_str = state["draft_plan"].model_dump_json(indent=2)

    # 记录输入
    input_data = {
        "critique": state["critique"],
        "original_plan": plan_json_str
    }
    log_and_print(f"修订者输入: {json.dumps(input_data, ensure_ascii=False, indent=2)}", "debug", "REVISER")
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位高级项目经理，擅长根据反馈迭代和优化项目计划。
            你的任务是根据批评意见，修改原始的JSON任务计划。
            你必须严格遵循原始的JSON格式，并且只输出JSON。
            
            {format_instructions}"""),
        ("human",
         """请根据以下批评意见，修订这份JSON计划。

            **批评意见**:
            {critique}

            **原始JSON计划**:
            ```json
            {original_plan}
            ```

            请输出一份完整的、经过修订的JSON计划。""")
    ])
    chain = prompt_template | model | parser
    try:
        invoke_data = {
            "critique": state["critique"],
            "original_plan": plan_json_str,
            "format_instructions": parser.get_format_instructions()
        }
        revised_plan_dict = chain.invoke(invoke_data)
        revised_plan = Plan.model_validate(revised_plan_dict)

        # 记录输出
        log_and_print(f"修订者输出: {json.dumps(revised_plan_dict, ensure_ascii=False, indent=2)}", "debug", "REVISER")
        log_and_print("修订完成，生成新版结构化计划草案", "info", "REVISER")

        current_count = state.get("revision_count", 0)
        return {"draft_plan": revised_plan, "critique": None, "revision_count": current_count + 1}
    except Exception as e:
        log_and_print(f"修订失败，返回原始计划。错误: {e}", "error", "REVISER")
        # 如果修订失败，为避免循环卡死，可以返回一个错误信息或保持原样
        # 这里我们选择保持原样，并保留批评意见，让迭代上限来终止循环
        return {}

# --- 6. LangGraph 边的决策逻辑 ---

def decide_to_research(state: TaskPlannerState) -> str:
    # ... (此处代码与Notebook中的 Cell 10 完全相同)
    if state["research_needed"]:
        return "researcher"
    else:
        if state.get("revision_count") is None:
            state["revision_count"] = 0
        return "critic"

def decide_to_revise(state: TaskPlannerState) -> str:
    # ... (此处代码与Notebook中的 Cell 10 完全相同)
    revision_count = state.get("revision_count", 0)
    critique = state.get("critique")

    log_and_print(f"当前修订次数: {revision_count}", "info", "DECISION")

    if revision_count >= MAX_REVISIONS:
        log_and_print(f"决策：已达到最大修订次数 ({MAX_REVISIONS})。流程强制结束", "warning", "DECISION")
        return END
    if critique is None:
        log_and_print("决策：计划通过审查，流程结束", "info", "DECISION")
        return END
    else:
        log_and_print("决策：计划需要修订", "info", "DECISION")
        return "reviser"

# --- 7. 图的构建与编译 (封装在函数中) ---

# 使用一个全局变量来缓存编译好的图，避免重复编译
_app = None

def create_task_planner_agent():
    """构建并编译 LangGraph 应用。"""
    global _app
    if _app is None:
        log_and_print("正在构建和编译任务规划智能体...", "info", "SYSTEM")
        workflow = StateGraph(TaskPlannerState)
        workflow.add_node("planner", planner_node)
        workflow.add_node("researcher", researcher_node)
        workflow.add_node("critic", critique_node)
        workflow.add_node("reviser", reviser_node)
        workflow.set_entry_point("planner")
        workflow.add_conditional_edges(
            "planner",
             decide_to_research,
             {"researcher": "researcher", "critic": "critic"}
        )
        workflow.add_edge("researcher", "planner")
        workflow.add_conditional_edges(
            "critic",
            decide_to_revise,
            {END: END, "reviser": "reviser"}
        )
        workflow.add_edge("reviser", "critic")
        _app = workflow.compile()
        log_and_print("智能体编译完成", "info", "SYSTEM")
    return _app

# --- 8. 公共API函数 (供其他模块调用) ---

def run_task_planner(goal: str, verbose: bool = True) -> dict:
    """
    运行任务规划智能体的主函数。

    Args:
        goal: 一个字符串，描述需要规划的高层次目标。
        verbose: 是否在终端显示详细信息，默认True

    Returns:
        一个字典，包含:
        - "plan": 一个 Plan Pydantic 对象，或在失败时为 None。
        - "critique": 如果因为达到迭代上限而退出，这里会包含未解决的批评意见。
    """
    # 设置verbose模式
    set_verbose(verbose)

    app = create_task_planner_agent()
    initial_state = {"original_goal": goal, "revision_count": 0}

    log_and_print(f"🚀 开始为目标规划任务: '{goal}'", "info", "MAIN")
    final_state = None
    for event in app.stream(initial_state, stream_mode="values"):
        final_state = event
    
    return {
        "plan": final_state.get("draft_plan"),
        "critique": final_state.get("critique")
    }


# --- 9. 独立运行的入口 ---

def main():
    """处理命令行参数并运行智能体。"""
    parser = argparse.ArgumentParser(description="任务规划智能体命令行工具。")
    parser.add_argument("goal", type=str, help="我需要开发一个智能体，我给出一堆规划的任务，将任务派发下去，智能体去规划使用工具完成每一个任务，完成任务后 检查是否完成，完成了我就派发下一个任务。请帮我设计方案")
    parser.add_argument("--verbose", "-v", action="store_true", default=True, help="显示详细的执行过程")
    parser.add_argument("--quiet", "-q", action="store_true", help="静默模式，只输出结果")
    args = parser.parse_args()

    # 设置verbose模式
    verbose = args.verbose and not args.quiet
    result = run_task_planner(args.goal, verbose=verbose)

    log_and_print("="*50, "info", "MAIN")
    log_and_print("✅ 任务规划流程结束", "info", "MAIN")
    log_and_print("="*50, "info", "MAIN")

    if result["plan"]:
        log_and_print("--- 最终任务计划 (JSON) ---", "info", "MAIN")
        # 使用 pydantic 的 model_dump_json 方法获得格式优美的JSON字符串
        plan_json = result["plan"].model_dump_json(indent=2)
        log_and_print(plan_json, "info", "MAIN")
    else:
        log_and_print("❌ 未能生成最终计划", "error", "MAIN")

    if result["critique"]:
        log_and_print("⚠️ 注意：智能体已达到最大迭代次数，但计划仍存在以下待解决问题：", "warning", "MAIN")
        log_and_print("--- 最终反馈 ---", "warning", "MAIN")
        log_and_print(result["critique"], "warning", "MAIN")

if __name__ == "__main__":
    main()