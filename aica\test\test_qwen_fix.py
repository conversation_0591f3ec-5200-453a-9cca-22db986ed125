#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试阿里云qwen-max-latest模型的修复效果
验证Pydantic验证器是否能正确处理字符串到数组的转换
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.state import CompetitorInfo
    from aica_agent.chains import competitor_identification_chain
    from aica_agent.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def test_competitor_info_validator():
    """测试CompetitorInfo的字段验证器"""
    print("=" * 60)
    print("测试CompetitorInfo字段验证器")
    print("=" * 60)
    
    # 测试1: 字符串格式的main_functions
    print("1. 测试字符串格式的main_functions...")
    try:
        competitor1 = CompetitorInfo(
            name="测试竞品1",
            company="测试公司",
            main_functions="中医电子病历、辅助开方、中医药知识库",  # 字符串格式
            description="测试描述",
            relevance_score=0.9
        )
        print(f"   ✓ 字符串转换成功: {competitor1.main_functions}")
        assert isinstance(competitor1.main_functions, list)
        assert len(competitor1.main_functions) == 3
        print(f"   ✓ 转换后的列表: {competitor1.main_functions}")
    except Exception as e:
        print(f"   ✗ 字符串转换失败: {e}")
        return False
    
    # 测试2: 列表格式的main_functions
    print("\n2. 测试列表格式的main_functions...")
    try:
        competitor2 = CompetitorInfo(
            name="测试竞品2",
            company="测试公司",
            main_functions=["功能1", "功能2", "功能3"],  # 列表格式
            description="测试描述",
            relevance_score=0.8
        )
        print(f"   ✓ 列表格式保持不变: {competitor2.main_functions}")
        assert isinstance(competitor2.main_functions, list)
        assert len(competitor2.main_functions) == 3
    except Exception as e:
        print(f"   ✗ 列表格式处理失败: {e}")
        return False
    
    # 测试3: 空字符串处理
    print("\n3. 测试空字符串处理...")
    try:
        competitor3 = CompetitorInfo(
            name="测试竞品3",
            company="测试公司",
            main_functions="",  # 空字符串
            description="测试描述",
            relevance_score=0.7
        )
        print(f"   ✓ 空字符串处理: {competitor3.main_functions}")
        assert isinstance(competitor3.main_functions, list)
        assert len(competitor3.main_functions) == 0
    except Exception as e:
        print(f"   ✗ 空字符串处理失败: {e}")
        return False
    
    return True


def test_competitor_identification_chain():
    """测试竞品识别chain是否能正常工作"""
    print("\n" + "=" * 60)
    print("测试竞品识别Chain")
    print("=" * 60)
    
    logger = get_logger()
    
    # 模拟搜索结果
    test_input = {
        "query": "中医辅助诊疗系统",
        "search_results": """1. 标题: 悬壶台中医辅助诊疗系统
   链接: https://cloud.guahao.cn/product/21
   摘要: 运用中医辨证论治系统结合互联网、人工智能技术，构建悬壶台中医健康信息云平台，提供中医电子病历、辅助开方、心脑血管疾病、中医药知识库。

2. 标题: 大经中医辅助诊疗系统
   链接: https://www.dajingtcm.com/CMAI/login.html
   摘要: 本系统利用人工智能算法提供的临床辅助决策信息，包括辨证结果分析、治疗方案推荐、中药加减化裁建议。"""
    }
    
    print(f"测试输入: {test_input['query']}")
    print("\n开始执行competitor_identification_chain...")
    print("观察是否还有Pydantic验证错误:")
    print("-" * 40)
    
    try:
        # 执行竞品识别chain
        result = competitor_identification_chain.invoke(test_input)
        
        print("-" * 40)
        print("执行完成!")
        
        if result is None:
            print("⚠ 结果为空，可能是API调用问题")
            return False
        
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'competitors'):
            print(f"✓ 识别到 {len(result.competitors)} 个竞品")
            
            for i, competitor in enumerate(result.competitors, 1):
                print(f"\n竞品 {i}:")
                print(f"  名称: {competitor.name}")
                print(f"  公司: {competitor.company}")
                print(f"  功能: {competitor.main_functions}")
                print(f"  相关性: {competitor.relevance_score}")
                
                # 验证main_functions是列表
                if not isinstance(competitor.main_functions, list):
                    print(f"  ✗ main_functions不是列表类型: {type(competitor.main_functions)}")
                    return False
                else:
                    print(f"  ✓ main_functions是列表类型，包含 {len(competitor.main_functions)} 个功能")
        else:
            print(f"✗ 结果格式异常: {result}")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n✗ 执行失败: {e}")
        print(f"错误类型: {type(e)}")
        return False


def main():
    """主函数"""
    print("阿里云qwen-max-latest模型修复测试")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    tests = [
        ("CompetitorInfo验证器测试", test_competitor_info_validator),
        ("竞品识别Chain测试", test_competitor_identification_chain),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 qwen-max-latest模型修复成功！")
        print("现在可以正确处理字符串格式的main_functions字段")
        print("Pydantic验证错误已解决")
        return True
    else:
        print("\n✗ 部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
