#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ollama模型的修复效果
验证竞品选择chain是否能正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.chains import competitor_selection_chain
    from aica_agent.state import CompetitorInfo
    from aica_agent.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def test_competitor_selection():
    """测试竞品选择chain"""
    print("=" * 60)
    print("测试竞品选择Chain修复效果")
    print("=" * 60)
    
    logger = get_logger()
    
    # 模拟竞品识别的输出
    test_input = {
        "query": "中医辅助诊疗系统",
        "identified_competitors": """名称: 中医智能辅助诊疗系统
公司: 南京大经中医药信息技术有限公司
功能: 名老中医经验智能化传承, 岐黄问道·大模型, 智能脉诊仪, 中医智能预问诊系统
描述: 提供全面的解决方案，包括智能化的传承、诊断与预问诊。
相关性: 0.9

名称: 中医临床智能辅助决策系统
公司: -
功能: 电子健康档案管理, 标准化诊疗流程, AI辅助诊断
描述: 为基层诊所提供便捷的电子健康档案管理和标准化诊疗流程，提升诊疗效率。
相关性: 0.85

名称: 中医大脑
公司: 问止中医
功能: AI辅助诊疗, 辨证论治
描述: 基于2008年开始研发的中医人工智能系统，提供可靠的人机结合疗效。
相关性: 0.9""",
        "max_competitors": "3"
    }
    
    print(f"测试输入: {test_input['query']}")
    print(f"竞品数量: 3个")
    print("\n开始执行competitor_selection_chain...")
    print("注意观察日志中是否还有Pydantic验证错误:")
    print("-" * 40)
    
    try:
        # 执行竞品选择chain
        result = competitor_selection_chain.invoke(test_input)
        
        print("-" * 40)
        print("执行完成!")
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'selected_competitors'):
            print(f"选择的竞品数量: {len(result.selected_competitors)}")
            print(f"选择理由: {result.selection_reasoning}")
            
            print("\n选择的竞品:")
            for i, competitor in enumerate(result.selected_competitors, 1):
                print(f"  {i}. {competitor.name} ({competitor.company})")
                print(f"     功能: {competitor.main_functions}")
                print(f"     相关性: {competitor.relevance_score}")
        else:
            print(f"结果: {result}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 执行被用户中断")
        return True
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        print(f"错误类型: {type(e)}")
        return False


def main():
    """主函数"""
    print("Ollama模型修复测试")
    
    success = test_competitor_selection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！")
        print("\n如果没有看到Pydantic验证错误，说明修复成功！")
        print("现在可以正常使用ollama模型进行竞品分析。")
    else:
        print("❌ 测试失败，需要进一步修复")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
