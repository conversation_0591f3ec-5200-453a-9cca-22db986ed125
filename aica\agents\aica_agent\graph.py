# aica_agent/graph.py
from langgraph.graph import StateGraph, END
from .state import AgentState
from .nodes import (
    planning_node,
    select_next_competitor_node,
    select_next_sub_task_node,
    subtask_execution_node,
    reflection_node,
    final_report_node,
)
from config import MAX_SUBTASK_RETRIES

def should_start_next_competitor(state: AgentState) -> str:
    """条件: 是否还有下一个竞品需要分析"""
    if state.current_competitor_name is None:
        return "yes_all_done" # 所有竞品都完成了
    return "no_more_competitors" # 开始分析当前竞品

def should_start_next_sub_task(state: AgentState) -> str:
    """条件: 当前竞品是否还有下一个子任务"""
    if state.current_sub_task_name is None:
        return "yes_sub_tasks_done" # 当前竞品的所有子任务完成
    return "no_more_sub_tasks" # 开始分析当前子任务

def did_sub_task_succeed(state: AgentState) -> str:
    """条件: 子任务是否通过验证，或是否已达最大重试次数"""
    is_complete = False
    for comp in state.analysis_plan:
        if comp.competitor_name == state.current_competitor_name:
            for task in comp.sub_tasks:
                if task.task_name == state.current_sub_task_name:
                    is_complete = task.is_complete
                    break
            break
            
    if is_complete:
        return "success"
    elif state.current_retry_count >= MAX_SUBTASK_RETRIES:
        print(f"      - [警告] 子任务 '{state.current_sub_task_name}' 已达最大重试次数，强制继续。")
        return "max_retries_reached"
    else:
        return "retry"

def build_graph():
    """组装并编译新的 Agent 图"""
    workflow = StateGraph(AgentState)

    # 添加所有节点
    workflow.add_node("planner", planning_node)
    workflow.add_node("next_competitor_selector", select_next_competitor_node)
    workflow.add_node("next_sub_task_selector", select_next_sub_task_node)
    workflow.add_node("executor", subtask_execution_node)
    workflow.add_node("reflector", reflection_node)
    workflow.add_node("reporter", final_report_node)

    # --- 设定图的入口和边的连接 ---
    workflow.set_entry_point("planner")
    
    # 规划完成后，选择第一个竞品
    workflow.add_edge("planner", "next_competitor_selector")

    # 外层循环：竞品分析
    workflow.add_conditional_edges(
        "next_competitor_selector",
        should_start_next_competitor,
        {
            "yes_all_done": "reporter", # 所有竞品完成，去生成报告
            "no_more_competitors": "next_sub_task_selector" # 开始分析这个竞品
        }
    )

    # 内层循环：子任务分析
    workflow.add_conditional_edges(
        "next_sub_task_selector",
        should_start_next_sub_task,
        {
            "yes_sub_tasks_done": "next_competitor_selector", # 子任务完成，去选下一个竞品
            "no_more_sub_tasks": "executor" # 开始执行这个子任务
        }
    )
    
    # 行动-反思循环
    workflow.add_edge("executor", "reflector")
    workflow.add_conditional_edges(
        "reflector",
        did_sub_task_succeed,
        {
            "success": "next_sub_task_selector", # 成功，去选下一个子任务
            "max_retries_reached": "next_sub_task_selector", # 达到最大重试，也继续
            "retry": "executor" # 失败，重试执行
        }
    )
    
    # 报告生成后结束
    workflow.add_edge("reporter", END)
    
    return workflow.compile()