#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示新的chain日志记录系统的实际执行效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.chains import planning_chain
    from aica_agent.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def demo_planning_chain():
    """演示planning chain的执行和日志输出"""
    print("=" * 60)
    print("演示Planning Chain执行和日志输出")
    print("=" * 60)
    
    logger = get_logger()
    
    # 测试输入
    test_input = {
        "initial_input": "中医辅助诊疗系统的竞品分析"
    }
    
    print(f"输入: {test_input}")
    print("\n开始执行planning_chain...")
    print("注意观察日志中的chain名称和阶段信息:")
    print("-" * 40)
    
    try:
        # 执行planning chain
        result = planning_chain.invoke(test_input)
        
        print("-" * 40)
        print("执行完成!")
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'dict'):
            result_dict = result.dict()
            print(f"竞品数量: {len(result_dict.get('competitors_to_research', []))}")
            print(f"子任务数量: {len(result_dict.get('sub_tasks', []))}")
            
            print("\n识别的竞品:")
            for i, competitor in enumerate(result_dict.get('competitors_to_research', []), 1):
                print(f"  {i}. {competitor}")
            
            print("\n子任务:")
            for i, task in enumerate(result_dict.get('sub_tasks', []), 1):
                print(f"  {i}. {task.get('task_name', 'Unknown')}")
        else:
            print(f"结果: {result}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 执行被用户中断")
        return True
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        return False


def demo_log_format():
    """演示日志格式说明"""
    print("\n" + "=" * 60)
    print("日志格式说明")
    print("=" * 60)
    
    print("新的日志格式包含以下信息:")
    print("1. [阶段名称] - 如 [PLANNING], [COMPETITOR_CONFIRMATION] 等")
    print("2. 🔗 开始执行Chain: [chain名称] - chain开始执行")
    print("3. 📥 Chain输入: [输入数据] - chain的输入参数")
    print("4. 📤 Chain输出: [输出数据] - chain的输出结果")
    print("5. ✅ 完成执行Chain: [chain名称] - chain执行完成")
    print("6. 🔧 开始/完成执行函数: [函数名称] - 函数执行状态")
    print("7. ❌ Chain执行失败 - 错误信息")
    print("8. 🛑 Chain被用户中断 - 用户中断信息")
    
    print("\n这样您可以清楚地看到:")
    print("- 当前执行的是哪个chain")
    print("- 当前处于哪个阶段")
    print("- 输入和输出的详细信息")
    print("- 执行状态和错误信息")


def main():
    """主函数"""
    print("Chain执行演示 - 新日志记录系统")
    
    # 演示日志格式
    demo_log_format()
    
    # 演示实际执行
    success = demo_planning_chain()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 演示完成！")
        print("\n现在您可以:")
        print("1. 查看控制台输出中的详细日志信息")
        print("2. 检查logs目录下的日志文件")
        print("3. 根据chain名称和阶段信息定位问题")
        print("4. 使用Ctrl+C安全中断程序执行")
    else:
        print("❌ 演示过程中出现错误")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 演示被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
