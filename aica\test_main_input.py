#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化测试main.py程序
"""

import subprocess
import sys
import time

def test_main_program():
    """测试主程序"""
    print("=" * 60)
    print("测试主程序 - 自动输入")
    print("=" * 60)
    
    # 准备输入
    inputs = [
        "1",  # 选择增强模式
        "中医辅助诊疗系统"  # 输入分析主题
    ]
    
    input_text = "\n".join(inputs) + "\n"
    
    try:
        # 启动主程序
        process = subprocess.Popen(
            ["D:/ProgramData/Miniconda3/envs/llms/python.exe", "main.py"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="."
        )
        
        print("程序已启动，发送输入...")
        print(f"输入内容: {inputs}")
        
        # 发送输入并等待
        stdout, stderr = process.communicate(input=input_text, timeout=300)  # 5分钟超时
        
        print("\n" + "=" * 40)
        print("程序输出:")
        print("=" * 40)
        print(stdout)
        
        if stderr:
            print("\n" + "=" * 40)
            print("错误输出:")
            print("=" * 40)
            print(stderr)
        
        print(f"\n程序退出码: {process.returncode}")
        
        if process.returncode == 0:
            print("✅ 程序执行成功!")
            return True
        else:
            print("❌ 程序执行失败!")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 程序执行超时 (5分钟)")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

if __name__ == "__main__":
    success = test_main_program()
    sys.exit(0 if success else 1)
