#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ollama基本连接和功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.chains import get_ollama_llm
    from langchain_core.prompts import ChatPromptTemplate
    from aica_agent.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def test_ollama_connection():
    """测试ollama连接"""
    print("=" * 60)
    print("测试Ollama连接")
    print("=" * 60)
    
    try:
        llm = get_ollama_llm()
        if llm is None:
            print("❌ Ollama LLM创建失败")
            return False
        
        print("✅ Ollama LLM创建成功")
        print(f"模型类型: {type(llm)}")
        return True
    except Exception as e:
        print(f"❌ Ollama连接失败: {e}")
        return False


def test_simple_invoke():
    """测试简单的LLM调用"""
    print("\n" + "=" * 60)
    print("测试简单LLM调用")
    print("=" * 60)
    
    try:
        llm = get_ollama_llm()
        if llm is None:
            print("❌ 无法获取LLM实例")
            return False
        
        # 简单的文本生成测试
        prompt = ChatPromptTemplate.from_template("请简单回答：什么是中医？")
        chain = prompt | llm
        
        print("发送简单问题：什么是中医？")
        print("等待回答...")
        
        result = chain.invoke({})
        print(f"✅ 收到回答: {result.content[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ 简单调用失败: {e}")
        return False


def test_structured_output():
    """测试结构化输出"""
    print("\n" + "=" * 60)
    print("测试结构化输出")
    print("=" * 60)
    
    try:
        from pydantic import BaseModel, Field
        from typing import List
        
        class SimpleResponse(BaseModel):
            answer: str = Field(description="回答内容")
            confidence: float = Field(description="置信度0-1")
        
        llm = get_ollama_llm()
        if llm is None:
            print("❌ 无法获取LLM实例")
            return False
        
        prompt = ChatPromptTemplate.from_template("""请回答问题并给出置信度。

问题：中医的核心理念是什么？

请按照以下JSON格式回答：
{{
  "answer": "你的回答",
  "confidence": 0.9
}}""")
        
        chain = prompt | llm.with_structured_output(SimpleResponse)
        
        print("发送结构化问题...")
        print("等待结构化回答...")
        
        result = chain.invoke({})
        if result:
            print(f"✅ 收到结构化回答:")
            print(f"   答案: {result.answer}")
            print(f"   置信度: {result.confidence}")
            return True
        else:
            print("❌ 收到空结果")
            return False
        
    except Exception as e:
        print(f"❌ 结构化输出测试失败: {e}")
        return False


def main():
    """主函数"""
    print("Ollama基本功能测试")
    
    tests = [
        ("Ollama连接", test_ollama_connection),
        ("简单LLM调用", test_simple_invoke),
        ("结构化输出", test_structured_output),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 Ollama基本功能正常！")
        return True
    else:
        print("❌ 部分功能异常，需要检查ollama服务")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
