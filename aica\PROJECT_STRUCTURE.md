# AICA 项目结构说明

## 项目概述
AICA (AI Competitive Analysis) 是一个基于AI的竞品分析系统，支持自动化的竞品识别、数据收集和分析报告生成。

## 目录结构

```
aica/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── requirements.txt           # Python依赖
├── .env                      # 环境变量配置
├── README.md                 # 项目说明
├── PROJECT_STRUCTURE.md      # 本文件
│
├── agents/                   # 智能体目录（重构后）
│   ├── __init__.py           # 包初始化
│   ├── task_planner.py       # 任务规划智能体（增强日志）
│   │
│   ├── aica_agent/           # 竞品分析智能体
│   │   ├── __init__.py
│   │   ├── chains.py         # LangChain链定义
│   │   ├── prompts.py        # 提示词模板
│   │   ├── state.py          # 数据结构定义
│   │   ├── nodes.py          # 基础节点实现
│   │   ├── enhanced_nodes.py # 增强节点实现
│   │   ├── graph.py          # 基础图结构
│   │   ├── enhanced_graph.py # 增强图结构
│   │   └── pdf_builder.py    # PDF报告生成
│   │
│   ├── tools/                # 共享工具模块
│   │   ├── __init__.py
│   │   ├── web_tools.py      # 网络工具（搜索、抓取）
│   │   └── search_tool.py    # 搜索工具
│   │
│   └── utils/                # 共享工具函数
│       ├── __init__.py
│       ├── logger.py         # 日志系统
│       ├── chain_logger.py   # Chain日志记录
│       ├── llm_wrapper.py    # LLM包装器（超时控制）
│       └── callbacks.py      # 回调处理器
│
├── test/                     # 测试文件
│   ├── README.md             # 测试说明
│   ├── test.ipynb            # 交互式测试
│   ├── test_new_structure.py # 新结构测试（新增）
│   ├── test_basic_functionality.py
│   ├── test_complete_workflow.py
│   ├── test_ollama_basic.py
│   ├── test_qwen_*.py        # qwen模型测试
│   ├── test_unicode_fix.py   # Unicode修复测试
│   ├── test_chain_logger.py  # 日志系统测试
│   └── test_simple_timeout.py # 超时控制测试
│
├── docs/                     # 文档目录
│   └── augment/              # Augment Agent交付报告
│       ├── final_delivery_report.md
│       ├── qwen_model_fix_report.md
│       ├── timeout_and_logging_enhancement_report.md
│       ├── project_cleanup_report.md
│       └── project_restructure_report.md（本次重构报告）
│
├── logs/                     # 运行日志
│   └── aica_run_*.log        # 自动生成的日志文件
│
├── reports/                  # 生成的分析报告
│   └── *.pdf                 # PDF格式报告
│
└── assets/                   # 静态资源
    └── TCCEB.TTF             # 中文字体文件
```

## 核心模块说明

### 1. 主程序 (main.py)
- 程序入口点
- 用户交互界面
- 模式选择（基础/增强）

### 2. 智能体目录 (agents/)
- **task_planner.py**: 任务规划智能体，支持verbose参数控制
- **aica_agent/**: 竞品分析智能体核心模块
  - **chains.py**: LangChain链的定义和配置
  - **prompts.py**: AI提示词模板
  - **state.py**: 数据结构和状态管理
  - **nodes.py / enhanced_nodes.py**: 处理节点实现
  - **graph.py / enhanced_graph.py**: 工作流图结构

### 3. 共享工具模块 (agents/tools/)
- **web_tools.py**: 网络搜索和抓取功能
- **search_tool.py**: 搜索工具实现

### 4. 共享工具函数 (agents/utils/)
- **logger.py**: 统一日志系统
- **chain_logger.py**: Chain执行日志和心跳监控
- **llm_wrapper.py**: LLM调用超时控制
- **callbacks.py**: 回调处理器

### 5. 报告生成 (pdf_builder.py)
- PDF格式报告生成
- 支持中文字体
- 结构化内容布局

## 主要功能

### 1. 竞品识别
- 基于搜索的竞品发现
- AI驱动的相关性分析
- 智能竞品筛选

### 2. 数据收集
- 自动化网页抓取
- 内容相关性评估
- 多源数据整合

### 3. 分析报告
- 结构化分析框架
- 多维度对比分析
- PDF格式输出

### 4. 系统增强
- 超时控制机制
- 详细日志记录
- 心跳监控系统
- 多模型支持

## 配置说明

### 环境变量 (.env)
```
# LLM API配置
QWEN_API_KEY=your_qwen_api_key
OPENROUTER_API_KEY=your_openrouter_key

# 搜索配置
USER_AGENT=your_user_agent

# 其他API密钥...
```

### Python环境
- Python 3.8+
- conda环境: llms
- 依赖包: requirements.txt

## 使用方式

### 1. 基础模式
```bash
python main.py
# 选择: 1 (基础模式)
# 输入: 竞品分析主题
```

### 2. 增强模式
```bash
python main.py
# 选择: 2 (增强模式)
# 输入: 竞品分析主题
```

### 3. 测试运行
```bash
python test/test_complete_workflow.py
```

## 日志和监控

- **运行日志**: logs/aica_run_*.log
- **心跳监控**: 每10秒输出执行状态
- **超时控制**: 防止程序卡死
- **详细追踪**: 完整的执行链路记录

## 扩展性

系统设计支持：
- 新增LLM模型
- 自定义分析维度
- 扩展数据源
- 定制报告格式
