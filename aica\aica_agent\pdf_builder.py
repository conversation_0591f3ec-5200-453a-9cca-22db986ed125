# aica_agent/pdf_builder.py
import os
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import inch
from config import REPORTS_DIR, REPORT_FILENAME, CHINESE_FONT_PATH

def build_pdf_with_reportlab(markdown_text: str) -> str:
    """
    Parses a Markdown string and builds a PDF using ReportLab.
    Returns the final filepath.
    """
    os.makedirs(REPORTS_DIR, exist_ok=True)
    filepath = os.path.join(REPORTS_DIR, REPORT_FILENAME)
    
    pdfmetrics.registerFont(TTFont('SimSun', CHINESE_FONT_PATH))
    styles = getSampleStyleSheet()
    styles['h1'].fontName = 'SimSun'
    styles['h2'].fontName = 'SimSun'
    styles['h3'].fontName = 'SimSun'
    styles['BodyText'].fontName = 'SimSun'
    styles['BodyText'].leading = 16

    story = []
    lines = markdown_text.split('\n')
    
    # ... (此处省略与上次回答中完全相同的 Markdown 解析和 Table 构建逻辑) ...
    in_table = False
    table_data = []

    for line in lines:
        line = line.strip()
        if line.startswith('### '):
            story.append(Paragraph(line.replace('### ', ''), styles['h3']))
            story.append(Spacer(1, 0.1 * inch))
        elif line.startswith('## '):
            story.append(Paragraph(line.replace('## ', ''), styles['h2']))
            story.append(Spacer(1, 0.1 * inch))
        elif line.startswith('# '):
            story.append(Paragraph(line.replace('# ', ''), styles['h1']))
            story.append(Spacer(1, 0.2 * inch))
        elif line.startswith('|') and line.endswith('|'): 
             row = [cell.strip() for cell in line.split('|')[1:-1]]
             if '---' in row[0]: 
                 continue
             table_data.append([Paragraph(cell, styles['BodyText']) for cell in row])
        elif table_data: 
            table = Table(table_data)
            style = TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmake),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), 'SimSun'), 
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ])
            table.setStyle(style)
            story.append(table)
            story.append(Spacer(1, 0.2 * inch))
            table_data = []
        elif line: 
            story.append(Paragraph(line, styles['BodyText']))
            story.append(Spacer(1, 0.1 * inch))
            
    doc = SimpleDocTemplate(filepath)
    doc.build(story)
    return filepath