# agents/__init__.py
"""
AICA 代理包

此包包含AICA系统的所有AI代理和共享工具。

结构:
- aica_agent/: 核心竞争分析代理
- tools/: 共享工具，用于网页抓取、搜索等
- utils/: 共享工具，用于日志记录、LLM封装等
- task_planner.py: 任务规划代理
"""

# Import shared utilities
from utils.logger import get_logger
from utils.chain_logger import create_logging_chain
from utils.llm_wrapper import create_llm_with_timeout

# Import shared tools
from tools.web_tools import *

__all__ = [
    'get_logger',
    'create_logging_chain', 
    'create_llm_with_timeout'
]
