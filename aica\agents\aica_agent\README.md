# AICA - AI Competitive Analysis System

🤖 基于 LangGraph 的智能竞品分析系统

## ✨ 核心特性

- 🔍 **智能竞品识别**: 基于搜索的自动竞品发现和筛选
- 📊 **深度数据收集**: 多源数据抓取和智能内容分析
- 🤖 **AI驱动分析**: 结构化竞品对比和洞察生成
- 📄 **专业报告**: PDF格式的详细分析报告
- ⚡ **超时控制**: 防卡死机制和实时状态监控
- 🔧 **多模型支持**: 兼容Ollama、OpenAI、阿里云等LLM
- 📝 **详细日志**: 完整的执行追踪和心跳监控

## 🚀 快速开始

### 1. 环境准备

```bash
# 激活conda环境
conda activate llms

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API密钥

创建 `.env` 文件：
```bash
# 阿里云通义千问
QWEN_API_KEY=your_qwen_api_key

# 或使用Ollama本地模型
# 确保ollama服务运行: ollama serve
```

### 3. 运行程序

```bash
# 运行主程序
python main.py

# 选择模式：
# 1 - 基础模式
# 2 - 增强模式（推荐）

# 输入分析主题，例如：中医辅助诊疗系统
```

### 4. 测试验证

```bash
# 基础功能测试
python test/test_basic_functionality.py

# 完整工作流测试
python test/test_complete_workflow.py

# 超时控制测试
python test/test_simple_timeout.py
```

## 📁 项目结构

```
aica/
├── main.py                    # 主程序入口
├── config.py                  # 配置文件
├── requirements.txt           # 依赖包
├── .env                      # API密钥配置
├── PROJECT_STRUCTURE.md      # 详细结构说明
│
├── aica_agent/               # 核心代码
│   ├── chains.py             # LangChain链定义
│   ├── prompts.py            # AI提示词模板
│   ├── state.py              # 数据结构
│   ├── nodes.py              # 处理节点
│   ├── enhanced_*.py         # 增强功能
│   ├── tools/                # 搜索和抓取工具
│   └── utils/                # 日志和超时控制
│
├── test/                     # 测试文件
├── docs/                     # 文档和报告
├── logs/                     # 运行日志
└── reports/                  # 生成的PDF报告
```

## 🎯 功能特色

### 智能竞品识别
- 基于搜索引擎的竞品发现
- AI驱动的相关性评估
- 自动筛选最有价值的竞品

### 深度数据收集
- 多关键词搜索策略
- 智能内容相关性判断
- 高质量网页内容抓取

### 专业分析报告
- 结构化对比分析
- 多维度竞品评估
- PDF格式专业输出

### 系统监控
- 实时执行状态显示
- 心跳监控防卡死
- 详细日志追踪

### 输出文件
- **PDF报告**: `reports/competitive_analysis_*.pdf`
- **运行日志**: `logs/aica_run_*.log`

## 📊 日志监控

### 实时状态显示
```
[17:29:58.961] [INFO] [LLM] [CALL_START] 开始调用planning_chain
[17:29:58.961] [INFO] [LLM] [TIMEOUT] 超时设置: 90秒
[17:30:08.975] [INFO] [LLM] [HEARTBEAT] planning_chain仍在执行, 已耗时: 10.0秒
[17:30:14.668] [INFO] [LLM] [CALL_END] planning_chain调用完成, 耗时: 15.71秒
```

### 日志类型说明
- `[CALL_START/END]` - LLM调用开始/结束
- `[HEARTBEAT]` - 心跳监控，确认程序运行
- `[TIMEOUT]` - 超时设置信息
- `[CHAIN_START/END]` - Chain执行状态
- `[WARNING]` - 长时间执行警告

## ⚙️ 系统配置

### 支持的LLM模型
- **Ollama**: qwen2.5:32b (本地部署)
- **阿里云**: qwen-max-latest (云端API)
- **OpenAI**: gpt-4 系列 (云端API)

### 超时控制
- **规划阶段**: 90秒超时
- **竞品识别**: 60秒超时
- **内容分析**: 60秒超时

## 🔧 故障排除

### 常见问题

1. **程序卡住不动**
   - 查看日志中的`[HEARTBEAT]`信息确认程序状态
   - 系统有90秒超时保护，会自动终止卡死的调用

2. **LLM连接失败**
   - 检查`.env`文件中的API密钥配置
   - 确认网络连接正常

3. **Unicode编码错误**
   - 系统已修复Windows GBK环境的编码问题
   - 所有emoji字符已替换为文本标识符

4. **搜索功能异常**
   - 检查网络连接
   - 确认搜索引擎可访问

### 调试方法

```bash
# 运行基础测试
python test/test_basic_functionality.py

# 检查超时控制
python test/test_simple_timeout.py

# 查看详细日志
tail -f logs/aica_run_*.log
```

## 📈 技术特色

- **防卡死机制**: 超时控制 + 心跳监控
- **多模型兼容**: 支持主流LLM服务
- **智能数据处理**: Pydantic验证器自动适配不同模型输出
- **完整日志追踪**: 从开始到结束的详细记录
- **用户友好**: 实时状态反馈，清晰的进度显示

## 📄 许可证

MIT License

---

**AICA - 让竞品分析更智能** 🚀
