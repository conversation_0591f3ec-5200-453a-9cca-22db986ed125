#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试callback修复是否解决了报错问题
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from aica_agent.utils.callbacks import AICACallbackHandler


def test_callback_robustness():
    """测试callback的健壮性"""
    print("测试callback处理各种异常情况...")
    
    callback = AICACallbackHandler("TEST_FIX")
    
    # 测试1: on_chain_start with None serialized
    print("1. 测试on_chain_start with None serialized...")
    try:
        callback.on_chain_start(None, {"test": "input"})
        print("   ✅ 处理None serialized成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试2: on_chain_start with non-dict serialized
    print("2. 测试on_chain_start with non-dict serialized...")
    try:
        callback.on_chain_start("not_a_dict", {"test": "input"})
        print("   ✅ 处理non-dict serialized成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试3: on_chain_start with None inputs
    print("3. 测试on_chain_start with None inputs...")
    try:
        callback.on_chain_start({"name": "TestChain"}, None)
        print("   ✅ 处理None inputs成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试4: on_chain_end with ChatPromptValue-like object
    print("4. 测试on_chain_end with ChatPromptValue-like object...")
    try:
        # 模拟ChatPromptValue对象
        mock_prompt_value = Mock()
        mock_prompt_value.__str__ = lambda: "Mock ChatPromptValue content"
        callback.on_chain_end(mock_prompt_value)
        print("   ✅ 处理ChatPromptValue-like object成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试5: on_chain_end with None outputs
    print("5. 测试on_chain_end with None outputs...")
    try:
        callback.on_chain_end(None)
        print("   ✅ 处理None outputs成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试6: on_chain_end with normal dict
    print("6. 测试on_chain_end with normal dict...")
    try:
        callback.on_chain_end({"result": "test output"})
        print("   ✅ 处理normal dict成功")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 测试7: _sanitize_inputs with non-dict input
    print("7. 测试_sanitize_inputs with non-dict input...")
    try:
        result = callback._sanitize_inputs("string input")
        if isinstance(result, dict) and "input" in result:
            print("   ✅ 处理non-dict input成功")
        else:
            print(f"   ❌ 返回格式错误: {result}")
            return False
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    print("\n🎉 所有callback健壮性测试通过！")
    return True


def test_original_error_scenarios():
    """测试原始错误场景"""
    print("\n测试原始错误场景...")
    
    callback = AICACallbackHandler("ERROR_SCENARIO_TEST")
    
    # 模拟原始错误1: 'NoneType' object has no attribute 'get'
    print("1. 模拟原始错误场景1...")
    try:
        callback.on_chain_start(None, {"query": "test"})
        print("   ✅ 原始错误1已修复")
    except AttributeError as e:
        if "'NoneType' object has no attribute 'get'" in str(e):
            print(f"   ❌ 原始错误1仍存在: {e}")
            return False
        else:
            print(f"   ❌ 新的AttributeError: {e}")
            return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False
    
    # 模拟原始错误2: 'ChatPromptValue' object has no attribute 'items'
    print("2. 模拟原始错误场景2...")
    try:
        # 创建一个模拟的ChatPromptValue对象
        class MockChatPromptValue:
            def __str__(self):
                return "Mock prompt value"
            
            def __repr__(self):
                return "MockChatPromptValue()"
        
        mock_output = MockChatPromptValue()
        callback.on_chain_end(mock_output)
        print("   ✅ 原始错误2已修复")
    except AttributeError as e:
        if "'ChatPromptValue' object has no attribute 'items'" in str(e):
            print(f"   ❌ 原始错误2仍存在: {e}")
            return False
        else:
            print(f"   ❌ 新的AttributeError: {e}")
            return False
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False
    
    print("\n🎉 原始错误场景测试通过！")
    return True


def main():
    """主测试函数"""
    print("=" * 60)
    print("Callback修复验证测试")
    print("=" * 60)
    
    success1 = test_callback_robustness()
    success2 = test_original_error_scenarios()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！Callback修复成功！")
        print("现在应该不会再出现以下错误：")
        print("- AttributeError: 'NoneType' object has no attribute 'get'")
        print("- AttributeError: 'ChatPromptValue' object has no attribute 'items'")
        return True
    else:
        print("❌ 部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
