#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode修复后的系统
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_import_without_unicode_error():
    """测试导入是否不再有Unicode错误"""
    print("=" * 60)
    print("测试Unicode修复")
    print("=" * 60)
    
    try:
        print("1. 测试导入chains模块...")
        from aica_agent.chains import planning_chain
        print("   ✓ chains模块导入成功")
        
        print("2. 测试导入main模块...")
        import main
        print("   ✓ main模块导入成功")
        
        print("3. 测试planning_chain执行...")
        test_input = {"initial_input": "中医辅助诊疗系统"}
        result = planning_chain.invoke(test_input)
        
        if result is not None:
            print("   ✓ planning_chain执行成功")
            if hasattr(result, 'competitors_to_research'):
                print(f"   ✓ 识别到 {len(result.competitors_to_research)} 个竞品")
            if hasattr(result, 'sub_tasks'):
                print(f"   ✓ 生成了 {len(result.sub_tasks)} 个子任务")
        else:
            print("   ⚠ planning_chain返回None")
        
        return True
        
    except UnicodeEncodeError as e:
        print(f"   ✗ Unicode编码错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def test_log_output():
    """测试日志输出是否正常"""
    print("\n" + "=" * 60)
    print("测试日志输出")
    print("=" * 60)
    
    try:
        from aica_agent.utils.logger import get_logger
        logger = get_logger()
        
        # 测试各种日志级别
        logger.info("[TEST] 这是一个测试信息", "TEST")
        logger.debug("[TEST] 这是一个调试信息", "TEST")
        logger.warning("[TEST] 这是一个警告信息", "TEST")
        logger.error("[TEST] 这是一个错误信息", "TEST")
        
        print("✓ 日志输出测试完成，无Unicode错误")
        return True
        
    except UnicodeEncodeError as e:
        print(f"✗ 日志Unicode编码错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 日志测试错误: {e}")
        return False


def main():
    """主函数"""
    print("Unicode修复验证测试")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    tests = [
        ("导入和执行测试", test_import_without_unicode_error),
        ("日志输出测试", test_log_output),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 Unicode修复成功！")
        print("系统现在可以在Windows GBK环境中正常运行")
        print("所有emoji字符已替换为文本标识符")
        return True
    else:
        print("\n✗ 部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
