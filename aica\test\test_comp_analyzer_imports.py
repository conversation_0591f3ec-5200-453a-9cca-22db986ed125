#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 agents/comp_analyzer 模块的导入路径修复
验证所有导入是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_comp_analyzer_imports():
    """测试comp_analyzer模块的导入"""
    print("=" * 60)
    print("测试 comp_analyzer 模块导入")
    print("=" * 60)
    
    try:
        print("1. 测试基础模块导入...")
        
        # 测试states模块导入
        print("   - 导入states模块...")
        from agents.comp_analyzer.states import (
            CompetitorAnalysisState, 
            CompetitorProfile, 
            AnalysisReport,
            RadarChartData
        )
        print("     ✓ states模块导入成功")
        
        # 测试config模块导入
        print("   - 导入config模块...")
        from agents.comp_analyzer.config import (
            LLM_MODEL, 
            LLM_TEMPERATURE, 
            MODEL_URL, 
            API_KEY
        )
        print("     ✓ config模块导入成功")
        print(f"     ✓ LLM模型: {LLM_MODEL}")
        
        # 测试agent模块导入
        print("   - 导入agent模块...")
        from agents.comp_analyzer.agent import logger, model, search_tool
        print("     ✓ agent模块导入成功")
        print(f"     ✓ 日志器初始化: {type(logger)}")
        print(f"     ✓ LLM模型初始化: {type(model)}")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def test_comp_analyzer_package():
    """测试comp_analyzer包的整体导入"""
    print("\n" + "=" * 60)
    print("测试 comp_analyzer 包导入")
    print("=" * 60)
    
    try:
        print("1. 测试包级别导入...")
        
        # 测试包导入
        import agents.comp_analyzer as comp_analyzer
        print("   ✓ 包导入成功")
        
        # 测试包中的类
        print("2. 测试包中的类和函数...")
        
        # 检查是否有必要的类
        required_classes = [
            'CompetitorAnalysisState',
            'CompetitorProfile', 
            'AnalysisReport',
            'RadarChartData'
        ]
        
        for class_name in required_classes:
            if hasattr(comp_analyzer, class_name):
                print(f"   ✓ {class_name} 可用")
            else:
                print(f"   ⚠ {class_name} 不可用")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ 包导入错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def test_shared_utils_imports():
    """测试共享utils模块的导入"""
    print("\n" + "=" * 60)
    print("测试共享utils模块导入")
    print("=" * 60)
    
    try:
        print("1. 测试logger导入...")
        from agents.utils.logger import get_logger
        logger = get_logger()
        print("   ✓ logger导入成功")
        
        print("2. 测试日志功能...")
        logger.info("这是一个测试日志", "TEST")
        print("   ✓ 日志功能正常")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def test_data_models():
    """测试数据模型的创建和使用"""
    print("\n" + "=" * 60)
    print("测试数据模型")
    print("=" * 60)
    
    try:
        from agents.comp_analyzer.states import (
            CompetitorAnalysisState, 
            CompetitorProfile, 
            AnalysisReport,
            RadarChartData
        )
        
        print("1. 测试CompetitorProfile创建...")
        profile = CompetitorProfile(
            name="测试竞品",
            company_overview="测试公司概述",
            key_features=["功能1", "功能2"],
            target_audience_profile="测试用户画像",
            pricing_model="测试定价模型",
            value_proposition="测试价值主张",
            market_perception="测试市场认知"
        )
        print(f"   ✓ CompetitorProfile创建成功: {profile.name}")
        
        print("2. 测试RadarChartData创建...")
        radar_data = RadarChartData(
            attributes=["功能", "价格", "易用性"],
            scores={"产品A": [8.0, 7.0, 9.0], "产品B": [7.0, 9.0, 8.0]}
        )
        print(f"   ✓ RadarChartData创建成功: {len(radar_data.attributes)}个维度")
        
        print("3. 测试CompetitorAnalysisState创建...")
        state = CompetitorAnalysisState(
            product_name="测试产品",
            competitor_count=3,
            known_competitors=["竞品1", "竞品2"],
            focus_features=["功能1", "功能2"]
        )
        print(f"   ✓ CompetitorAnalysisState创建成功: {state.product_name}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 数据模型测试失败: {e}")
        return False


def test_config_values():
    """测试配置值的正确性"""
    print("\n" + "=" * 60)
    print("测试配置值")
    print("=" * 60)
    
    try:
        from agents.comp_analyzer.config import (
            LLM_MODEL, 
            LLM_TEMPERATURE, 
            MODEL_URL, 
            API_KEY
        )
        
        print("1. 检查配置值...")
        print(f"   - LLM模型: {LLM_MODEL}")
        print(f"   - 温度设置: {LLM_TEMPERATURE}")
        print(f"   - API地址: {MODEL_URL}")
        print(f"   - API密钥: {'已设置' if API_KEY else '未设置'}")
        
        # 验证配置值的合理性
        if LLM_MODEL and isinstance(LLM_MODEL, str):
            print("   ✓ LLM模型配置正确")
        else:
            print("   ⚠ LLM模型配置异常")
            
        if 0 <= LLM_TEMPERATURE <= 2:
            print("   ✓ 温度设置合理")
        else:
            print("   ⚠ 温度设置可能不合理")
            
        if MODEL_URL and MODEL_URL.startswith('http'):
            print("   ✓ API地址格式正确")
        else:
            print("   ⚠ API地址格式异常")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 配置测试失败: {e}")
        return False


def main():
    """主函数"""
    print("comp_analyzer 导入路径修复测试")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    tests = [
        ("comp_analyzer模块导入测试", test_comp_analyzer_imports),
        ("comp_analyzer包导入测试", test_comp_analyzer_package),
        ("共享utils模块导入测试", test_shared_utils_imports),
        ("数据模型测试", test_data_models),
        ("配置值测试", test_config_values),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 comp_analyzer 导入路径修复成功！")
        print("\n修复内容:")
        print("- ✓ 统一使用相对导入路径")
        print("- ✓ 移除sys.path操作")
        print("- ✓ 修复logger导入路径")
        print("- ✓ 更新__init__.py文件")
        print("- ✓ 所有模块可正常导入和使用")
        return True
    else:
        print("\n✗ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
