# aica_agent/config.py
"""
存放智能体的全局配置，如模型名称、温度等。
"""
import os
from pathlib import Path
import sys
from agents.utils.log import log_and_print
# 获取项目根目录路径
root_dir = str(Path(__file__).parents[2])
# 将项目根目录添加到系统路径
if root_dir not in sys.path:
    sys.path.append(root_dir)

try:
    from dotenv import load_dotenv
    load_dotenv()
    log_and_print("环境变量已加载", "info")
except ImportError:
    log_and_print("python-dotenv 未安装，将从环境中直接读取API密钥", "warning")

# 推荐使用能力更强的模型以支持复杂分析
LLM_MODEL = "deepseek-r1-0528" 
# 使用deepseek模型的免费版
MODEL_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
# 较低的温度以确保输出的稳定性和事实性
LLM_TEMPERATURE = 0.1  # 这个参数控制模型输出中的随机性

API_KEY = os.getenv("QWEN_API_KEY")
