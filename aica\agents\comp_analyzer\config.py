# aica_agent/config.py
"""
存放智能体的全局配置，如模型名称、温度等。
"""
import os

import sys
from pathlib import Path

# 动态添加 utils 目录到 sys.path
utils_dir = str(Path(__file__).parent.parent / "utils")
if utils_dir not in sys.path:
    sys.path.append(utils_dir)

try:
    from dotenv import load_dotenv
    load_dotenv()
    from utils.logger import get_logger
    get_logger().info("环境变量已加载")
except ImportError:
    from utils.logger import get_logger
    get_logger().warning("python-dotenv 未安装，将从环境中直接读取API密钥")

# 推荐使用能力更强的模型以支持复杂分析
LLM_MODEL = "deepseek-r1-0528" 
# 使用deepseek模型的免费版
MODEL_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
# 较低的温度以确保输出的稳定性和事实性
LLM_TEMPERATURE = 0.1  # 这个参数控制模型输出中的随机性

API_KEY = os.getenv("QWEN_API_KEY")
