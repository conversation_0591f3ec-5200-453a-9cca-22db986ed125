# AICA 智能体开发指南

**版本**: v1.0  
**更新时间**: 2025年8月18日  
**适用环境**: Python 3.9+, llms conda环境  

## 📋 概述

本指南详细介绍如何在AICA系统中新增智能体，包括开发、配置、测试和调试的完整流程。

## 🏗️ 项目架构

### 目录结构
```
aica/
├── main.py                    # 主入口，智能体选择器
├── agents/                    # 智能体目录
│   ├── __init__.py
│   ├── utils/                 # 共享工具
│   │   ├── logger.py          # 日志系统
│   │   ├── chain_logger.py    # Chain日志
│   │   ├── llm_wrapper.py     # LLM封装
│   │   └── callbacks.py       # 回调函数
│   ├── tools/                 # 共享工具
│   │   ├── web_tools.py       # 网页工具
│   │   └── search_tool.py     # 搜索工具
│   ├── task_planner.py        # 任务规划智能体
│   ├── comp_analyzer/         # 竞品分析智能体
│   │   ├── __init__.py
│   │   ├── agent.py           # 主逻辑
│   │   ├── config.py          # 配置
│   │   └── states.py          # 数据模型
│   └── aica_agent/            # 传统竞品分析智能体
│       └── ...
├── test/                      # 测试文件
├── docs/                      # 文档目录
└── logs/                      # 日志目录
```

### 智能体注册机制

所有智能体通过 `main.py` 中的 `AGENT_REGISTRY` 注册：

```python
AGENT_REGISTRY: Dict[str, Dict[str, Any]] = {
    "agent_key": {
        "name": "智能体显示名称",
        "description": "智能体功能描述",
        "module": "agents.module_name",
        "function": "entry_function_name",
        "params": {
            "param_name": {
                "type": "str|int|bool|list|choice",
                "prompt": "用户输入提示",
                "required": True|False,
                "default": "默认值",
                "choices": ["选项1", "选项2"]  # 仅choice类型需要
            }
        }
    }
}
```

## 🚀 新增智能体步骤

### 第一步：创建智能体目录

1. **创建智能体目录**
```bash
mkdir agents/your_agent_name
```

2. **创建必要文件**
```bash
touch agents/your_agent_name/__init__.py
touch agents/your_agent_name/agent.py
touch agents/your_agent_name/config.py
touch agents/your_agent_name/states.py  # 可选，如果需要复杂数据模型
```

### 第二步：实现智能体核心逻辑

#### 1. config.py - 配置文件
```python
# agents/your_agent_name/config.py
"""
智能体配置文件
"""
import os
from ..utils.logger import get_logger

# 初始化日志
logger = get_logger()

try:
    from dotenv import load_dotenv
    load_dotenv()
    logger.info("环境变量已加载", "CONFIG")
except ImportError:
    logger.warning("python-dotenv 未安装，将从环境中直接读取API密钥", "CONFIG")

# 模型配置
LLM_MODEL = "deepseek-r1-0528"  # 或其他模型
MODEL_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
LLM_TEMPERATURE = 0.1
API_KEY = os.getenv("QWEN_API_KEY")

# 智能体特定配置
YOUR_AGENT_SPECIFIC_CONFIG = "value"
```

#### 2. states.py - 数据模型（可选）
```python
# agents/your_agent_name/states.py
"""
数据模型定义
"""
from typing import List, Optional, Dict
from pydantic import BaseModel, Field

class YourDataModel(BaseModel):
    """你的数据模型"""
    field1: str = Field(..., description="字段1描述")
    field2: List[str] = Field(default=[], description="字段2描述")
    field3: Optional[Dict] = None

class YourAgentState(BaseModel):
    """智能体状态模型"""
    input_data: str
    processing_status: str = "not_started"
    results: Optional[YourDataModel] = None
```

#### 3. agent.py - 主逻辑文件
```python
# agents/your_agent_name/agent.py
"""
智能体主逻辑
"""
import os
import sys
from typing import Optional, List, Dict, Any

# 处理导入路径，支持独立运行和包导入
def setup_imports():
    """设置导入路径，支持独立运行和包导入"""
    if __name__ == "__main__":
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

setup_imports()

# 内部模块导入 - 支持两种导入方式
try:
    # 尝试相对导入（包导入时）
    from .config import LLM_MODEL, API_KEY, YOUR_AGENT_SPECIFIC_CONFIG
    from .states import YourDataModel, YourAgentState
    from ..utils.logger import get_logger
except ImportError:
    # 回退到绝对导入（独立运行时）
    from agents.your_agent_name.config import LLM_MODEL, API_KEY, YOUR_AGENT_SPECIFIC_CONFIG
    from agents.your_agent_name.states import YourDataModel, YourAgentState
    from agents.utils.logger import get_logger

# 外部依赖
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

# 初始化
logger = get_logger()
model = ChatOpenAI(
    model=LLM_MODEL,
    api_key=API_KEY,
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
    temperature=0.1
)

def your_core_function(input_data: str, param1: str = "default") -> Dict[str, Any]:
    """
    智能体核心功能函数
    
    Args:
        input_data: 输入数据
        param1: 参数1
    
    Returns:
        处理结果字典
    """
    logger.info(f"开始处理: {input_data}")
    
    try:
        # 你的核心逻辑
        prompt = ChatPromptTemplate.from_template(
            "你是一个专业的AI助手。请处理以下输入：{input_data}"
        )
        
        chain = prompt | model
        result = chain.invoke({"input_data": input_data})
        
        logger.info("处理完成")
        return {
            "status": "success",
            "result": result.content,
            "input": input_data
        }
        
    except Exception as e:
        logger.error(f"处理失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "input": input_data
        }

def run_your_agent(input_data: str, param1: str = "default", param2: int = 10) -> Dict[str, Any]:
    """
    智能体主入口函数 - 这个函数会被main.py调用
    
    Args:
        input_data: 必需参数
        param1: 可选参数1
        param2: 可选参数2
    
    Returns:
        执行结果
    """
    logger.info(f"启动智能体，参数: input_data={input_data}, param1={param1}, param2={param2}")
    
    print(f"🚀 开始执行智能体...")
    print(f"📝 输入数据: {input_data}")
    
    result = your_core_function(input_data, param1)
    
    if result["status"] == "success":
        print(f"✅ 执行成功!")
        print(f"📊 结果: {result['result'][:100]}...")  # 显示前100字符
    else:
        print(f"❌ 执行失败: {result['error']}")
    
    return result

# 独立运行支持
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="你的智能体描述")
    parser.add_argument("--input_data", type=str, required=True, help="输入数据")
    parser.add_argument("--param1", type=str, default="default", help="参数1")
    parser.add_argument("--param2", type=int, default=10, help="参数2")
    
    args = parser.parse_args()
    
    result = run_your_agent(args.input_data, args.param1, args.param2)
    print(f"\n最终结果: {result}")
```

#### 4. __init__.py - 包初始化
```python
# agents/your_agent_name/__init__.py
"""
Your Agent Name

你的智能体描述
"""

from .agent import *
from .states import *
from .config import *

__all__ = [
    'run_your_agent',
    'YourDataModel',
    'YourAgentState',
    'LLM_MODEL',
    'API_KEY'
]
```

### 第三步：注册智能体

在 `main.py` 的 `AGENT_REGISTRY` 中添加你的智能体：

```python
AGENT_REGISTRY: Dict[str, Dict[str, Any]] = {
    # ... 现有智能体 ...
    
    "your_agent": {
        "name": "你的智能体名称",
        "description": "智能体功能的详细描述",
        "module": "agents.your_agent_name.agent",
        "function": "run_your_agent",
        "params": {
            "input_data": {
                "type": "str",
                "prompt": "请输入数据：",
                "required": True
            },
            "param1": {
                "type": "str",
                "prompt": "请输入参数1 (可选)：",
                "default": "default"
            },
            "param2": {
                "type": "int",
                "prompt": "请输入参数2 (默认10)：",
                "default": 10
            }
        }
    }
}
```

### 第四步：创建测试文件

```python
# test/test_your_agent.py
"""
你的智能体测试文件
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_your_agent_import():
    """测试智能体导入"""
    try:
        from agents.your_agent_name.agent import run_your_agent
        print("✅ 智能体导入成功")
        return True
    except Exception as e:
        print(f"❌ 智能体导入失败: {e}")
        return False

def test_your_agent_function():
    """测试智能体功能"""
    try:
        from agents.your_agent_name.agent import run_your_agent
        
        result = run_your_agent(
            input_data="测试输入",
            param1="测试参数",
            param2=5
        )
        
        if result and "status" in result:
            print("✅ 智能体功能测试成功")
            print(f"📊 结果: {result}")
            return True
        else:
            print("❌ 智能体返回结果格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 智能体功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("你的智能体测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_your_agent_import),
        ("功能测试", test_your_agent_function),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

## 🧪 测试和调试

### 1. 独立运行测试
```bash
# 测试智能体独立运行
& D:/ProgramData/Miniconda3/envs/llms/python.exe agents/your_agent_name/agent.py --input_data "测试" --param1 "值1"
```

### 2. 包导入测试
```bash
# 测试包导入
& D:/ProgramData/Miniconda3/envs/llms/python.exe -c "from agents.your_agent_name.agent import run_your_agent; print('导入成功')"
```

### 3. 运行测试文件
```bash
# 运行测试文件
& D:/ProgramData/Miniconda3/envs/llms/python.exe test/test_your_agent.py
```

### 4. 通过main.py测试
```bash
# 通过主程序测试
& D:/ProgramData/Miniconda3/envs/llms/python.exe main.py
# 然后选择你的智能体
```

## 🔧 调试技巧

### 1. 日志调试
```python
# 在代码中添加详细日志
logger.info("开始处理", "YOUR_AGENT")
logger.debug(f"输入参数: {params}", "YOUR_AGENT")
logger.error(f"处理失败: {error}", "YOUR_AGENT")
```

### 2. 断点调试
```python
# 添加断点进行调试
import pdb; pdb.set_trace()
```

### 3. 异常处理
```python
try:
    # 你的代码
    pass
except Exception as e:
    logger.error(f"详细错误信息: {e}", "YOUR_AGENT")
    import traceback
    logger.error(f"错误堆栈: {traceback.format_exc()}", "YOUR_AGENT")
    raise
```

## 📋 检查清单

### 开发完成检查
- [ ] 创建了智能体目录和所有必要文件
- [ ] 实现了核心功能函数
- [ ] 支持独立运行和包导入
- [ ] 在main.py中注册了智能体
- [ ] 创建了测试文件
- [ ] 所有测试通过

### 代码质量检查
- [ ] 使用了统一的日志系统
- [ ] 添加了适当的错误处理
- [ ] 函数有清晰的文档字符串
- [ ] 参数类型注解完整
- [ ] 遵循了项目的代码规范

### 功能检查
- [ ] 智能体能独立运行
- [ ] 智能体能通过main.py调用
- [ ] 参数处理正确
- [ ] 返回结果格式正确
- [ ] 日志记录完整

## 🚨 常见问题

### 1. 导入错误
**问题**: `ModuleNotFoundError: No module named 'agents.your_agent_name'`
**解决**: 检查__init__.py文件是否存在，路径是否正确

### 2. 相对导入失败
**问题**: `ImportError: attempted relative import with no known parent package`
**解决**: 使用提供的导入模板，支持两种导入方式

### 3. 智能体不显示在菜单中
**问题**: 智能体没有出现在main.py的选择菜单中
**解决**: 检查AGENT_REGISTRY中的注册信息是否正确

### 4. 参数类型错误
**问题**: 用户输入的参数类型不正确
**解决**: 在get_user_input函数中添加更严格的类型检查

## 📚 参考资源

- [LangChain文档](https://python.langchain.com/)
- [Pydantic文档](https://docs.pydantic.dev/)
- [Python类型注解](https://docs.python.org/3/library/typing.html)

---

**注意**: 请确保在llms conda环境中开发和测试，使用指定的Python解释器：
```bash
& D:/ProgramData/Miniconda3/envs/llms/python.exe
```
