# aica_agent/utils/callbacks.py
"""
<PERSON><PERSON><PERSON><PERSON> Callback 处理器，用于详细记录LLM调用过程
"""

from typing import Any, Dict, List, Optional, Union
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.messages import BaseMessage
from langchain_core.outputs import LLMResult
from .logger import get_logger
import json
import time


class AICACallbackHandler(BaseCallbackHandler):
    """AICA专用的Callback处理器"""
    
    def __init__(self, phase: str = "UNKNOWN"):
        self.phase = phase
        self.logger = get_logger()
        self.start_time = None
        self.token_count = 0
        
    def on_llm_start(
        self, 
        serialized: Dict[str, Any], 
        prompts: List[str], 
        **kwargs: Any
    ) -> None:
        """LLM开始调用时的回调"""
        self.start_time = time.time()
        self.logger.debug(f"🤖 LLM调用开始", self.phase)
        self.logger.debug(f"模型信息: {serialized.get('name', 'Unknown')}", self.phase)
        
        # 记录提示词（截断显示）
        for i, prompt in enumerate(prompts):
            prompt_preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
            self.logger.debug(f"提示词 {i+1}: {prompt_preview}", self.phase)
    
    def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        """LLM调用结束时的回调"""
        duration = time.time() - self.start_time if self.start_time else 0
        
        self.logger.debug(f"🤖 LLM调用完成，耗时: {duration:.2f}秒", self.phase)
        
        # 记录生成的内容
        for i, generation in enumerate(response.generations):
            for j, gen in enumerate(generation):
                content_preview = gen.text[:200] + "..." if len(gen.text) > 200 else gen.text
                self.logger.debug(f"生成内容 {i+1}-{j+1}: {content_preview}", self.phase)
        
        # 记录token使用情况
        if response.llm_output and 'token_usage' in response.llm_output:
            token_usage = response.llm_output['token_usage']
            self.logger.debug(f"Token使用: {token_usage}", self.phase)
    
    def on_llm_error(
        self,
        error: Union[Exception, KeyboardInterrupt],
        **kwargs: Any
    ) -> None:
        """LLM调用出错时的回调"""
        if isinstance(error, KeyboardInterrupt):
            self.logger.info(f"🤖 LLM调用被用户中断", self.phase)
            raise error  # 重新抛出KeyboardInterrupt，确保可以正常终止
        else:
            self.logger.error(f"🤖 LLM调用失败: {error}", self.phase)
    
    def on_chain_start(
        self,
        serialized: Dict[str, Any],
        inputs: Dict[str, Any],
        **kwargs: Any
    ) -> None:
        """Chain开始执行时的回调"""
        # 安全处理serialized参数
        if serialized is not None and isinstance(serialized, dict):
            chain_name = serialized.get('name', 'Unknown Chain')
        else:
            chain_name = 'Unknown Chain'
        self.logger.debug(f"🔗 Chain开始执行: {chain_name}", self.phase)

        # 记录输入参数（敏感信息处理）
        if inputs is not None:
            safe_inputs = self._sanitize_inputs(inputs)
            self.logger.debug(f"Chain输入: {safe_inputs}", self.phase)
    
    def on_chain_end(self, outputs: Any, **kwargs: Any) -> None:
        """Chain执行结束时的回调"""
        self.logger.debug(f"🔗 Chain执行完成", self.phase)

        # 记录输出结果（截断显示）
        # 安全处理outputs参数，可能是字典、ChatPromptValue或其他类型
        try:
            if outputs is not None:
                if isinstance(outputs, dict):
                    safe_outputs = self._sanitize_outputs(outputs)
                else:
                    # 对于非字典类型，转换为字符串表示
                    safe_outputs = {"output": str(outputs)[:300] + "..." if len(str(outputs)) > 300 else str(outputs)}
                self.logger.debug(f"Chain输出: {safe_outputs}", self.phase)
        except Exception as e:
            self.logger.debug(f"Chain输出记录失败: {e}", self.phase)
    
    def on_chain_error(
        self,
        error: Union[Exception, KeyboardInterrupt],
        **kwargs: Any
    ) -> None:
        """Chain执行出错时的回调"""
        if isinstance(error, KeyboardInterrupt):
            self.logger.info(f"🔗 Chain执行被用户中断", self.phase)
            raise error  # 重新抛出KeyboardInterrupt，确保可以正常终止
        else:
            self.logger.error(f"🔗 Chain执行失败: {error}", self.phase)
    
    def on_tool_start(
        self, 
        serialized: Dict[str, Any], 
        input_str: str, 
        **kwargs: Any
    ) -> None:
        """工具开始执行时的回调"""
        tool_name = serialized.get('name', 'Unknown Tool')
        self.logger.debug(f"🔧 工具开始执行: {tool_name}", self.phase)
        self.logger.debug(f"工具输入: {input_str[:100]}...", self.phase)
    
    def on_tool_end(self, output: str, **kwargs: Any) -> None:
        """工具执行结束时的回调"""
        self.logger.debug(f"🔧 工具执行完成", self.phase)
        output_preview = output[:200] + "..." if len(output) > 200 else output
        self.logger.debug(f"工具输出: {output_preview}", self.phase)
    
    def on_tool_error(
        self,
        error: Union[Exception, KeyboardInterrupt],
        **kwargs: Any
    ) -> None:
        """工具执行出错时的回调"""
        if isinstance(error, KeyboardInterrupt):
            self.logger.info(f"🔧 工具执行被用户中断", self.phase)
            raise error  # 重新抛出KeyboardInterrupt，确保可以正常终止
        else:
            self.logger.error(f"🔧 工具执行失败: {error}", self.phase)
    
    def on_text(self, text: str, **kwargs: Any) -> None:
        """处理文本时的回调"""
        if text.strip():  # 只记录非空文本
            text_preview = text[:100] + "..." if len(text) > 100 else text
            self.logger.debug(f"📝 处理文本: {text_preview}", self.phase)
    
    def _sanitize_inputs(self, inputs: Any) -> Dict[str, Any]:
        """清理输入数据，避免记录过长或敏感信息"""
        if not isinstance(inputs, dict):
            # 如果inputs不是字典，转换为字典格式
            return {"input": str(inputs)[:300] + "..." if len(str(inputs)) > 300 else str(inputs)}

        sanitized = {}
        for key, value in inputs.items():
            if isinstance(value, str):
                if len(value) > 300:
                    sanitized[key] = value[:300] + f"... (截断，原长度: {len(value)})"
                else:
                    sanitized[key] = value
            elif isinstance(value, (list, dict)):
                sanitized[key] = f"<{type(value).__name__} 对象，长度: {len(value)}>"
            else:
                sanitized[key] = str(value)
        return sanitized
    
    def _sanitize_outputs(self, outputs: Dict[str, Any]) -> Dict[str, Any]:
        """清理输出数据，避免记录过长信息"""
        sanitized = {}
        for key, value in outputs.items():
            if isinstance(value, str):
                if len(value) > 300:
                    sanitized[key] = value[:300] + f"... (截断，原长度: {len(value)})"
                else:
                    sanitized[key] = value
            elif isinstance(value, (list, dict)):
                sanitized[key] = f"<{type(value).__name__} 对象，长度: {len(value)}>"
            else:
                sanitized[key] = str(value)
        return sanitized


class PhaseCallbackManager:
    """阶段性Callback管理器"""
    
    @staticmethod
    def get_callbacks(phase: str) -> List[BaseCallbackHandler]:
        """获取指定阶段的callback列表"""
        return [AICACallbackHandler(phase)]
    
    @staticmethod
    def get_planning_callbacks() -> List[BaseCallbackHandler]:
        """获取规划阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("PLANNING")
    
    @staticmethod
    def get_competitor_confirmation_callbacks() -> List[BaseCallbackHandler]:
        """获取竞品确认阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("COMPETITOR_CONFIRMATION")
    
    @staticmethod
    def get_detailed_research_callbacks() -> List[BaseCallbackHandler]:
        """获取详细研究阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("DETAILED_RESEARCH")
    
    @staticmethod
    def get_execution_callbacks() -> List[BaseCallbackHandler]:
        """获取执行阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("EXECUTION")
    
    @staticmethod
    def get_reflection_callbacks() -> List[BaseCallbackHandler]:
        """获取反思阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("REFLECTION")
    
    @staticmethod
    def get_reporting_callbacks() -> List[BaseCallbackHandler]:
        """获取报告生成阶段的callbacks"""
        return PhaseCallbackManager.get_callbacks("REPORTING")


# 便捷函数
def create_callback_handler(phase: str) -> AICACallbackHandler:
    """创建指定阶段的callback处理器"""
    return AICACallbackHandler(phase)

def get_phase_callbacks(phase: str) -> List[BaseCallbackHandler]:
    """获取指定阶段的callback列表"""
    return PhaseCallbackManager.get_callbacks(phase)
