# aica_agent/enhanced_nodes.py - 增强的节点实现
import json
import os
from datetime import datetime
from typing import List, Dict, Any
from .state import AgentState, CompetitorInfo, SearchResult, CompetitorResearch, DetailedContent
from .tools import ddgs_search_tool, scrape_website_tool
from .chains import (
    competitor_identification_chain,
    competitor_selection_chain,
    keyword_generation_chain,
    content_relevance_chain,
    content_analysis_chain,
)
from .utils.logger import get_logger
from .utils.callbacks import PhaseCallbackManager
from config import (
    INITIAL_SEARCH_RESULTS, 
    SELECTED_COMPETITORS, 
    KEYWORDS_PER_COMPETITOR,
    MAX_PAGES_PER_KEYWORD,
    CONTENT_ANALYSIS_THRESHOLD
)

# 节点 1: 竞品确认阶段
def competitor_confirmation_node(state: AgentState) -> dict:
    """竞品确认节点：搜索并识别相关竞品"""
    logger = get_logger()
    logger.planning("=" * 60)
    logger.planning("开始竞品确认阶段")
    logger.planning("=" * 60)
    
    user_query = state.initial_input
    logger.planning(f"用户查询: {user_query}")
    
    # 第一步：执行初始搜索
    logger.search(f"执行初始搜索，目标结果数: {INITIAL_SEARCH_RESULTS}")
    search_results = ddgs_search_tool.invoke({
        "query": user_query, 
        "max_results": INITIAL_SEARCH_RESULTS
    })
    
    # 转换为SearchResult对象
    search_result_objects = []
    for result in search_results:
        search_result_objects.append(SearchResult(
            title=result["title"],
            url=result["href"],
            snippet=result["body"],
            relevance_score=0.8  # 默认评分，后续可以优化
        ))
    
    logger.search(f"获得 {len(search_result_objects)} 个搜索结果")
    
    # 第二步：识别竞品
    logger.planning("开始识别竞品信息...")
    
    # 准备搜索结果文本
    search_results_text = ""
    for i, result in enumerate(search_result_objects):
        search_results_text += f"{i+1}. 标题: {result.title}\n"
        search_results_text += f"   链接: {result.url}\n"
        search_results_text += f"   摘要: {result.snippet}\n\n"
    
    # 调用竞品识别链
    callbacks = PhaseCallbackManager.get_competitor_confirmation_callbacks()
    identification_result = competitor_identification_chain.invoke(
        {
            "query": user_query,
            "search_results": search_results_text
        },
        config={"callbacks": callbacks}
    )
    
    identified_competitors = identification_result.competitors
    logger.planning(f"识别出 {len(identified_competitors)} 个相关竞品")
    
    for competitor in identified_competitors:
        logger.planning(f"- {competitor.name} ({competitor.company}) - 相关性: {competitor.relevance_score}")
    
    # 第三步：选择深度研究的竞品
    logger.planning(f"选择 {SELECTED_COMPETITORS} 个竞品进行深度研究...")
    
    # 准备竞品信息文本
    competitors_text = ""
    for competitor in identified_competitors:
        competitors_text += f"名称: {competitor.name}\n"
        competitors_text += f"公司: {competitor.company}\n"
        competitors_text += f"功能: {', '.join(competitor.main_functions)}\n"
        competitors_text += f"描述: {competitor.description}\n"
        competitors_text += f"相关性: {competitor.relevance_score}\n\n"
    
    # 调用竞品选择链
    selection_result = competitor_selection_chain.invoke(
        {
            "query": user_query,
            "identified_competitors": competitors_text,
            "max_competitors": SELECTED_COMPETITORS
        },
        config={"callbacks": callbacks}
    )
    
    selected_competitors = selection_result.selected_competitors
    logger.planning(f"选择了 {len(selected_competitors)} 个竞品进行深度研究")
    logger.planning(f"选择理由: {selection_result.selection_reasoning}")
    
    for competitor in selected_competitors:
        logger.planning(f"✓ {competitor.name} ({competitor.company})")
    
    # 记录输入输出
    logger.log_input_output(
        "COMPETITOR_CONFIRMATION",
        {"user_query": user_query, "search_results_count": len(search_result_objects)},
        {
            "identified_competitors_count": len(identified_competitors),
            "selected_competitors_count": len(selected_competitors),
            "selected_competitors": [c.name for c in selected_competitors]
        }
    )
    
    return {
        "initial_search_results": search_result_objects,
        "identified_competitors": identified_competitors,
        "selected_competitors": selected_competitors
    }

# 节点 2: 详细资料搜寻
def detailed_research_node(state: AgentState) -> dict:
    """详细研究节点：为每个选定竞品进行深度信息收集"""
    logger = get_logger()
    logger.execution("=" * 60)
    logger.execution("开始详细资料搜寻阶段")
    logger.execution("=" * 60)
    
    selected_competitors = state.selected_competitors
    competitor_research_results = []
    
    for i, competitor in enumerate(selected_competitors):
        logger.execution(f"正在研究竞品 {i+1}/{len(selected_competitors)}: {competitor.name}")
        
        # 第一步：生成搜索关键词
        logger.execution("生成搜索关键词...")
        
        callbacks = PhaseCallbackManager.get_detailed_research_callbacks()
        keyword_result = keyword_generation_chain.invoke(
            {
                "competitor_name": competitor.name,
                "competitor_company": competitor.company,
                "main_functions": ", ".join(competitor.main_functions),
                "description": competitor.description,
                "num_keywords": KEYWORDS_PER_COMPETITOR
            },
            config={"callbacks": callbacks}
        )
        
        keywords = keyword_result.keywords
        logger.execution(f"生成了 {len(keywords)} 个关键词: {keywords}")
        logger.execution(f"关键词策略: {keyword_result.reasoning}")
        
        # 第二步：为每个关键词搜索和筛选内容
        collected_contents = []
        
        for keyword in keywords:
            logger.search(f"搜索关键词: {keyword}")
            
            # 搜索相关内容
            search_results = ddgs_search_tool.invoke({
                "query": f"{competitor.name} {keyword}",
                "max_results": MAX_PAGES_PER_KEYWORD
            })
            
            logger.search(f"关键词 '{keyword}' 获得 {len(search_results)} 个结果")
            
            # 判断每个搜索结果的相关性
            for result in search_results:
                logger.scrape(f"分析内容相关性: {result['title'][:50]}...")
                
                # 调用相关性判断链
                relevance_result = content_relevance_chain.invoke(
                    {
                        "competitor_name": competitor.name,
                        "keyword": keyword,
                        "title": result["title"],
                        "url": result["href"],
                        "snippet": result["body"]
                    },
                    config={"callbacks": callbacks}
                )
                
                logger.scrape(f"相关性评分: {relevance_result.relevance_score}")
                logger.scrape(f"判断理由: {relevance_result.reasoning}")
                
                # 如果相关性足够高，则抓取完整内容
                if relevance_result.is_relevant and relevance_result.relevance_score >= CONTENT_ANALYSIS_THRESHOLD:
                    logger.scrape(f"抓取网页内容: {result['href']}")
                    
                    # 抓取网页内容
                    full_content = scrape_website_tool.invoke(result["href"])
                    
                    # 创建详细内容对象
                    detailed_content = DetailedContent(
                        url=result["href"],
                        title=result["title"],
                        content=full_content,
                        keywords=[keyword],
                        summary=result["body"]
                    )
                    
                    collected_contents.append(detailed_content)
                    logger.scrape(f"✓ 已收集内容，长度: {len(full_content)} 字符")
                else:
                    logger.scrape(f"✗ 跳过低相关性内容")
        
        # 第三步：分析和总结收集的内容
        logger.execution(f"分析收集的内容，共 {len(collected_contents)} 个网页")
        
        if collected_contents:
            # 准备内容文本
            content_text = ""
            for content in collected_contents:
                content_text += f"=== {content.title} ===\n"
                content_text += f"URL: {content.url}\n"
                content_text += f"内容: {content.content[:2000]}...\n\n"  # 限制长度
            
            # 调用内容分析链
            analysis_result = content_analysis_chain.invoke(
                {
                    "competitor_name": competitor.name,
                    "collected_content": content_text
                },
                config={"callbacks": callbacks}
            )
            
            analysis_summary = f"""
## {competitor.name} 分析总结

**整体总结:** {analysis_result.summary}

**关键洞察:**
{chr(10).join([f"- {insight}" for insight in analysis_result.key_insights])}

**产品特性:**
{chr(10).join([f"- {feature}" for feature in analysis_result.product_features])}

**商业模式:** {analysis_result.business_model}

**用户反馈:**
{chr(10).join([f"- {feedback}" for feedback in analysis_result.user_feedback])}

**竞争优势:**
{chr(10).join([f"- {advantage}" for advantage in analysis_result.competitive_advantages])}
"""
        else:
            analysis_summary = f"未能收集到足够的相关内容进行分析。"
        
        # 创建竞品研究对象
        research = CompetitorResearch(
            competitor_info=competitor,
            search_keywords=keywords,
            collected_contents=collected_contents,
            analysis_summary=analysis_summary,
            is_complete=True
        )
        
        competitor_research_results.append(research)
        
        logger.execution(f"✓ 完成 {competitor.name} 的详细研究")
        logger.execution(f"  - 关键词数: {len(keywords)}")
        logger.execution(f"  - 收集内容数: {len(collected_contents)}")
        logger.execution(f"  - 分析总结长度: {len(analysis_summary)} 字符")
    
    # 记录输入输出
    logger.log_input_output(
        "DETAILED_RESEARCH",
        {"selected_competitors": [c.name for c in selected_competitors]},
        {
            "research_results_count": len(competitor_research_results),
            "total_collected_contents": sum(len(r.collected_contents) for r in competitor_research_results)
        }
    )
    
    logger.execution("=" * 60)
    logger.execution("详细资料搜寻阶段完成")
    logger.execution("=" * 60)
    
    return {
        "competitor_research": competitor_research_results
    }
