#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的超时控制测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试基本功能和日志")
    print("=" * 60)
    
    try:
        print("1. 导入模块...")
        from aica_agent.chains import planning_chain
        print("   ✓ 导入成功")
        
        print("2. 准备测试输入...")
        test_input = {
            "initial_input": "中医辅助诊疗系统"
        }
        print("   ✓ 输入准备完成")
        
        print("3. 执行planning_chain...")
        print("   注意观察日志中的心跳监控和超时控制:")
        
        import time
        start_time = time.time()
        
        result = planning_chain.invoke(test_input)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"   ✓ 执行完成，耗时: {duration:.2f}秒")
        
        if result:
            print(f"   ✓ 获得结果: {type(result)}")
            if hasattr(result, 'competitors_to_research'):
                print(f"   ✓ 识别竞品: {len(result.competitors_to_research)}个")
            if hasattr(result, 'sub_tasks'):
                print(f"   ✓ 生成任务: {len(result.sub_tasks)}个")
        else:
            print("   ⚠ 结果为空")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 执行失败: {e}")
        print(f"   错误类型: {type(e)}")
        return False


def main():
    """主函数"""
    print("简单超时控制测试")
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ 测试完成！")
        print("\n请检查日志输出中是否包含:")
        print("- [HEARTBEAT] 心跳监控信息")
        print("- [TIMESTAMP] 时间戳信息")
        print("- [DURATION] 执行耗时信息")
        print("- [LLM] LLM调用详细信息")
    else:
        print("✗ 测试失败")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
