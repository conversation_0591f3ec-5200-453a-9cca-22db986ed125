#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的项目结构
验证agents目录结构和task_planner的日志功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """测试新的导入结构"""
    print("=" * 60)
    print("测试新的项目结构导入")
    print("=" * 60)
    
    try:
        print("1. 测试agents包导入...")
        import agents
        print("   ✓ agents包导入成功")
        
        print("2. 测试共享工具导入...")
        from agents.tools import ddgs_search_tool, scrape_website_tool
        print("   ✓ 共享工具导入成功")
        
        print("3. 测试共享utils导入...")
        from agents.utils import get_logger, create_logging_chain
        print("   ✓ 共享utils导入成功")
        
        print("4. 测试aica_agent导入...")
        from agents.aica_agent import planning_chain
        print("   ✓ aica_agent导入成功")
        
        print("5. 测试task_planner导入...")
        from agents.task_planner import run_task_planner, set_verbose
        print("   ✓ task_planner导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def test_task_planner_verbose():
    """测试task_planner的verbose功能"""
    print("\n" + "=" * 60)
    print("测试task_planner的verbose功能")
    print("=" * 60)
    
    try:
        from agents.task_planner import run_task_planner, set_verbose
        
        print("1. 测试verbose=True模式...")
        print("   (应该在终端显示详细信息)")
        
        # 测试一个简单的目标
        test_goal = "创建一个简单的待办事项管理系统"
        
        print(f"   目标: {test_goal}")
        print("   开始执行...")
        
        result = run_task_planner(test_goal, verbose=True)
        
        if result and result.get("plan"):
            print("   ✓ 规划成功生成")
            print(f"   ✓ 计划包含 {len(result['plan'].tasks)} 个任务")
        else:
            print("   ⚠ 规划结果为空或失败")
        
        print("\n2. 测试verbose=False模式...")
        print("   (应该只在日志中记录，终端输出较少)")
        
        result2 = run_task_planner(test_goal, verbose=False)
        
        if result2 and result2.get("plan"):
            print("   ✓ 静默模式规划成功")
        else:
            print("   ⚠ 静默模式规划失败")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")
        return False


def test_logging_system():
    """测试日志系统"""
    print("\n" + "=" * 60)
    print("测试日志系统")
    print("=" * 60)
    
    try:
        from agents.utils.logger import get_logger
        
        logger = get_logger()
        
        print("1. 测试日志记录...")
        logger.info("[TEST] 这是一个测试信息", "TEST")
        logger.debug("[TEST] 这是一个调试信息", "TEST")
        logger.warning("[TEST] 这是一个警告信息", "TEST")
        logger.error("[TEST] 这是一个错误信息", "TEST")
        
        print("   ✓ 日志记录测试完成")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 日志测试失败: {e}")
        return False


def test_main_program():
    """测试主程序是否能正常运行"""
    print("\n" + "=" * 60)
    print("测试主程序导入")
    print("=" * 60)
    
    try:
        print("1. 测试main.py导入...")
        import main
        print("   ✓ main.py导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ✗ main.py导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ✗ 其他错误: {e}")
        return False


def main():
    """主函数"""
    print("新项目结构测试")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    tests = [
        ("项目结构导入测试", test_imports),
        ("日志系统测试", test_logging_system),
        ("主程序导入测试", test_main_program),
        ("task_planner verbose功能测试", test_task_planner_verbose),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("\n🎉 新项目结构测试通过！")
        print("\n新结构特点:")
        print("- ✓ agents目录统一管理所有智能体")
        print("- ✓ tools和utils作为共享模块")
        print("- ✓ task_planner支持verbose参数控制")
        print("- ✓ 日志系统完整记录所有chain的输入输出")
        print("- ✓ 终端输出可通过verbose控制")
        return True
    else:
        print("\n✗ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
