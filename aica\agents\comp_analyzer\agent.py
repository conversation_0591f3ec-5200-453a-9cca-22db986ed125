# agents/comp_analyzer/agent.py
"""
包含AI竞品分析师智能体的核心逻辑，包括所有LangGraph节点、边和图的定义。
"""
import json
from functools import wraps
import argparse

# 内部模块导入
from .config import LLM_MODEL, LLM_TEMPERATURE, MODEL_URL, API_KEY
from .states import CompetitorAnalysisState, CompetitorProfile, AnalysisReport
from ..utils.logger import get_logger

# LangChain 和 LangGraph 的核心组件
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langgraph.graph import END, StateGraph
from pydantic import BaseModel

# 初始化
logger = get_logger()
model = ChatOpenAI(
    model=LLM_MODEL, 
    api_key=API_KEY,
    openai_api_base=MODEL_URL,
    temperature=LLM_TEMPERATURE
)
search_tool = DuckDuckGoSearchRun(name="web_search")

# --- 日志装饰器 ---
def log_node_execution(node_func):
    @wraps(node_func)
    def wrapper(state: dict) -> dict:
        node_name = node_func.__name__
        logger.info(f"节点 '{node_name}' 开始执行", phase='EXECUTION')
        logger.debug(f"输入状态: {logger._format_state(state)}", phase='EXECUTION')
        output_update = node_func(state)
        logger.debug(f"节点更新: {logger._format_state(output_update)}", phase='EXECUTION')
        final_state = {**state, **(output_update or {})}
        logger.debug(f"输出状态: {logger._format_state(final_state)}", phase='EXECUTION')
        logger.info(f"节点 '{node_name}' 执行完成", phase='EXECUTION')
        return output_update
    return wrapper

# --- LangGraph 节点定义 ---
@log_node_execution
def product_profiler_node(state: CompetitorAnalysisState) -> dict:
    logger.search(f"开始深度研究我们的产品: {state['product_name']}")
    parser = JsonOutputParser(pydantic_object=BaseModel.model_validate(
        {
            "profile": {"summary": "string", "value_proposition": "string"},
            "swot": {"Strengths": [], "Weaknesses": [], "Opportunities": [], "Threats": []}
        }
    ))
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", "你是一位顶尖的产品战略顾问。你的任务是深入研究一个产品，并为其提供一份产品档案和一份详细的SWOT分析。\n{format_instructions}"),
            ("human", "请为产品 '{product_name}' 提供产品档案（摘要和价值主张）和SWOT分析。请利用搜索工具获取信息。")
        ]
    )
    chain = prompt | model | parser
    result = chain.invoke(
        {
            "product_name": state['product_name'], 
            "format_instructions": parser.get_format_instructions()
        }
    )
    logger.result("产品档案和SWOT分析已完成。")
    return {"our_product_profile": result['profile'], "swot_analysis": result['swot']}

@log_node_execution
def macro_analyzer_node(state: CompetitorAnalysisState) -> dict:
    logger.search(f"开始对 '{state['product_name']}' 所在市场进行宏观分析")
    parser = JsonOutputParser(pydantic_object=BaseModel.model_validate(
        {
            "PEST_analysis": {
                "Political": [], 
                "Economic": [], 
                "Social": [], 
                "Technological": []
            },
            "Porters_Five_Forces": {
                "Threat_of_New_Entrants": [], 
                "Bargaining_Power_of_Buyers": [], 
                "Bargaining_Power_of_Suppliers": [],
                "Threat_of_Substitute_Products": [], 
                "Industry_Rivalry": []
            }
        }
    ))
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", "你是一位专业的市场分析师，精通PEST和波特五力等战略模型。\n{format_instructions}"),
            ("human", "请为 '{product_name}' 所处的行业进行详细的宏观环境分析，包括PEST和波特五力模型。")
        ]
    )
    chain = prompt | model | parser
    result = chain.invoke(
        {
            "product_name": state['product_name'], 
            "format_instructions": parser.get_format_instructions()
        }
    )
    logger.result("宏观环境分析已完成。")
    return {"macro_analysis": result}

@log_node_execution
def competitor_fusion_node(state: CompetitorAnalysisState) -> dict:
    known = state.get("known_competitors") or []
    needed_count = state["competitor_count"]
    final_competitors = set(known)
    logger.planning(f"必选竞品列表: {list(final_competitors) if final_competitors else '无'}")
    if needed_count > 0:
        logger.search(f"需要额外寻找 {needed_count} 个新竞品...")
        prompt = ChatPromptTemplate.from_template("你是一位市场研究员。请为产品 '{product_name}' 找出 {needed_count} 个主要的竞争对手。请确保不要返回以下任何一个已知竞品: {exclude_list}。请只返回一个用逗号分隔的列表。")
        chain = prompt | model
        result = chain.invoke(
            {
                "product_name": state["product_name"], 
                "needed_count": needed_count, 
                "exclude_list": ", ".join(final_competitors)
            }).content
        newly_found = [c.strip() for c in result.split(',') if c.strip()]
        logger.result(f"新找到的竞品: {newly_found}")
        final_competitors.update(newly_found)
    final_list = list(final_competitors)
    logger.planning(f"最终确定的竞品分析总列表: {final_list}")
    return {"competitor_list": final_list, "research_data": state.get("research_data", {})}

@log_node_execution
def prepare_competitor_analysis_node(state: CompetitorAnalysisState) -> dict:
    competitor_list = state.get("competitor_list", [])
    research_data = state.get("research_data", {})
    next_competitor = next((c for c in competitor_list if c not in research_data), None)
    logger.planning(f"准备分析竞品: '{next_competitor}'")
    return {"competitor_to_analyze": next_competitor}

@log_node_execution
def single_competitor_profiler_node(state: CompetitorAnalysisState) -> dict:
    competitor_name = state["competitor_to_analyze"]
    logger.search(f"开始为竞品 '{competitor_name}' 构建深度画像...")
    parser = JsonOutputParser(pydantic_object=CompetitorProfile)
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", "你是一位顶尖的竞品情报分析师，擅长从公开信息中构建深度竞品画像。\n{format_instructions}"),
            ("human", "请为竞品 '{competitor_name}' 构建一份详细的画像。请特别关注以下用户可能关心的功能点，并在'key_features'中体现出来: {focus_features_str}。")
        ]
    )
    chain = prompt | model | parser
    analysis_result = chain.invoke(
        {
            "competitor_name": competitor_name, 
            "focus_features_str": ", ".join(state.get("focus_features") or []) or "无特定关注功能", 
            "format_instructions": parser.get_format_instructions()
        }
    )
    current_data = state.get("research_data", {})
    current_data[competitor_name] = CompetitorProfile.model_validate(analysis_result)
    logger.result(f"竞品 '{competitor_name}' 的画像构建完成。")
    return {"research_data": current_data, "competitor_to_analyze": None}

@log_node_execution
def report_synthesizer_node(state: CompetitorAnalysisState) -> dict:
    logger.reflection("开始合成所有分析数据，生成最终战略报告...")
    parser = JsonOutputParser(pydantic_object=AnalysisReport)
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", "你是一位首席战略官 (CSO)，擅长将复杂的市场和竞品数据整合成清晰、富有洞察力的战略报告。你的报告需要包含可供前端直接使用的可视化数据。\n{format_instructions}"),
            ("human", "请基于以下全部数据，生成一份专业级竞品分析报告。**特别注意**：在生成`radar_chart_data`时，请确保`attributes`列表至少包含5个有意义的对比维度，\
                并为我们自己的产品 '{product_name}' 和所有竞品打分。\n\n**全部输入数据**:\n{full_state_json}")
        ]
    )
    chain = prompt | model | parser
    state_for_prompt = state.copy(); state_for_prompt.pop('final_report', None)
    report = chain.invoke(
        {
            "product_name": state["product_name"], 
            "full_state_json": json.dumps(state_for_prompt, default=lambda o: o.model_dump() if isinstance(o, BaseModel) else str(o), indent=2), 
            "format_instructions": parser.get_format_instructions()
        }
    )
    logger.result("最终战略报告已成功生成。")
    return {"final_report": AnalysisReport.model_validate(report)}

# --- 边的决策逻辑 ---
def should_continue_analysis(state: CompetitorAnalysisState) -> str:
    logger.planning("进入决策点：是否继续分析...")
    competitor_list = state.get("competitor_list", [])
    research_data = state.get("research_data", {})
    if len(research_data) < len(competitor_list):
        logger.planning("决策：是，还有未分析的竞品。")
        return "continue_analysis"
    else:
        logger.planning("决策：否，所有竞品已分析完毕。")
        return "generate_report"

# --- 图的构建与编译 ---
_app = None
def create_analyzer_agent():
    global _app
    if _app is None:
        logger.info("正在构建专业级AI竞品分析师智能体...", phase="SETUP")
        workflow = StateGraph(CompetitorAnalysisState)
        workflow.add_node("product_profiler", product_profiler_node)
        workflow.add_node("macro_analyzer", macro_analyzer_node)
        workflow.add_node("competitor_fuser", competitor_fusion_node)
        workflow.add_node("prepare_for_profiling", prepare_competitor_analysis_node)
        workflow.add_node("single_competitor_profiler", single_competitor_profiler_node)
        workflow.add_node("report_synthesizer", report_synthesizer_node)
        workflow.set_entry_point("product_profiler")
        workflow.add_edge("product_profiler", "macro_analyzer")
        workflow.add_edge("macro_analyzer", "competitor_fuser")
        workflow.add_conditional_edges(
            "competitor_fuser", should_continue_analysis, 
            {
                "continue_analysis": "prepare_for_profiling", 
                "generate_report": "report_synthesizer"
            }
        )
        workflow.add_edge("prepare_for_profiling", "single_competitor_profiler")
        workflow.add_edge("single_competitor_profiler", "competitor_fuser")
        workflow.add_edge("report_synthesizer", END)
        _app = workflow.compile()
        logger.info("智能体编译完成。", phase="SETUP")
    return _app

# --- 公共API函数 ---
def run_competitor_analysis(product_name: str, competitor_count: int, known_competitors: Optional[List[str]] = None, focus_features: Optional[List[str]] = None) -> dict:
    app = create_analyzer_agent()
    initial_state = {
        "product_name": product_name, 
        "competitor_count": competitor_count, 
        "known_competitors": known_competitors or [], 
        "focus_features": focus_features or [], 
        "research_data": {}
    }
    logger.info(f"开始为产品 '{product_name}' 进行全面竞品分析...")
    final_state = None
    for event in app.stream(initial_state, stream_mode="values"):
        final_state = event
    return {"report": final_state.get("final_report")}

def run():
    """处理命令行参数并运行智能体。"""

    parser = argparse.ArgumentParser(
        description="AI竞品分析师智能体 (V3 专业咨询版)。",
        formatter_class=argparse.RawTextHelpFormatter # 保持帮助文本格式
    )
    parser.add_argument("--product_name", type=str, default="中医辅助诊疗系统", help="您自己的产品名称。")
    parser.add_argument("--competitor_count", type=int, default=3, help="需要额外调研的新竞品数量。")

    parser.add_argument(
        "--known-competitors", 
        nargs='+', 
        default=[],
        help="需要强制分析的已知竞品列表，用空格分隔。\n示例: --known-competitors 'Sketch' 'Adobe XD'"
    )
    parser.add_argument(
        "--focus-features", 
        nargs='+', 
        default=[],
        help="需要重点关注的功能点列表，用空格分隔。\n示例: --focus-features '实时协作' '插件生态'"
    )
    args = parser.parse_args()

    result = run_competitor_analysis(
        args.product_name, 
        args.competitor_count,
        args.known_competitors,
        args.focus_features
    )
    
    logger.info("="*50, phase="FINAL")
    logger.info("✅ 竞品分析流程结束", phase="FINAL")
    logger.info("="*50, phase="FINAL")
    
    if result.get("report"):
        report_json = result["report"].model_dump_json(indent=2, ensure_ascii=False)
        logger.result("\n" + report_json)
        
        # 将报告保存到文件
        report_filename = f"competitor_report_{args.product_name.replace(' ', '_')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_json)
        logger.info(f"最终报告已保存到: {report_filename}", phase="FINAL")
    else:
        logger.error("未能生成最终报告。", phase="FINAL")

if __name__ == "__main__":
    run()