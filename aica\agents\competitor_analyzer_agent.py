"""
AI竞品分析师智能体 (AI Competitor Analyst Agent) - 已集成 AICALogger

该模块既可以作为一个独立的命令行工具运行，也可以被其他Python脚本导入调用。
它接收一个产品名称和需要分析的竞品数量，然后自动执行研究、分析，
并最终输出一份结构化的、供AI产品经理使用的竞品分析报告。
所有步骤都通过 AICALogger 进行详细记录。
"""
import os
import json
import argparse
from functools import wraps
from typing import List, Optional, Dict, TypedDict

# Pydantic 用于定义和验证数据结构
from pydantic import BaseModel, Field

# LangChain 和 LangGraph 的核心组件
from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langgraph.graph import END, StateGraph

# --- 1. 导入并初始化自定义日志器 ---
from utils.logger import get_logger

# 全局日志器实例
logger = get_logger()
def log_and_print(message: str, level: str = "info", phase: str = "TASK_PLANNER"):
    """统一的日志和打印函数"""
    # 日志中始终记录
    if level == "info":
        logger.info(f"[{phase}] {message}", phase)
    elif level == "warning":
        logger.warning(f"[{phase}] {message}", phase)
    elif level == "error":
        logger.error(f"[{phase}] {message}", phase)
    elif level == "debug":
        logger.debug(f"[{phase}] {message}", phase)
# --- 2. 全局配置与模型定义 ---
try:
    from dotenv import load_dotenv
    load_dotenv()
    log_and_print("环境变量已加载", "info")
except ImportError:
    log_and_print("python-dotenv 未安装，将从环境中直接读取API密钥", "warning")
# ===============================

# --- 3. Pydantic 模型 (与之前相同) ---

class CompetitorInfo(BaseModel):
    name: str = Field(..., description="竞品的名称。")
    core_features: List[str] = Field(..., description="该产品的核心功能列表。")
    pricing_strategy: str = Field(..., description="定价策略概述（如免费增值、订阅制、按需付费等）。")
    strengths: List[str] = Field(..., description="相对于我们产品的优势。")
    weaknesses: List[str] = Field(..., description="相对于我们产品的劣势。")
    target_audience: str = Field(..., description="主要目标用户群体。")

class AnalysisReport(BaseModel):
    product_summary: str = Field(..., description="对我们自己产品的简要概述。")
    market_positioning_summary: str = Field(..., description="对整体市场格局和我们产品定位的总结。")
    feature_comparison_matrix: Dict[str, List[str]]
    competitors_analysis: List[CompetitorInfo]
    strategic_recommendations: List[str]

# --- 4. LangGraph 状态定义 (与之前相同) ---

class CompetitorAnalysisState(TypedDict):
    product_name: str
    competitor_count: int
    product_summary: Optional[str]
    competitor_list: List[str]
    research_data: Dict[str, CompetitorInfo]
    competitor_to_analyze: Optional[str]
    final_report: Optional[AnalysisReport]

# --- 5. 核心组件（模型和工具）初始化 ---

model = model = ChatOpenAI(
    model="deepseek-r1-0528",
    api_key=os.getenv("QWEN_API_KEY"),
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
    temperature=0
)
search_tool = DuckDuckGoSearchRun(name="web_search")


# --- 6. 日志装饰器：自动记录节点输入输出 ---
def log_node_execution(node_func):
    """一个装饰器，用于自动记录LangGraph节点的完整输入和输出状态。"""
    @wraps(node_func)
    def wrapper(state: dict) -> dict:
        node_name = node_func.__name__
        logger.info(f"节点 '{node_name}' 开始执行", phase='EXECUTION')
        # 记录完整的输入状态
        logger.debug(f"输入状态: {logger._format_state(state)}", phase='EXECUTION')
        
        # 执行原始节点函数
        output_update = node_func(state)
        
        # 记录节点返回的更新内容
        logger.debug(f"节点更新: {logger._format_state(output_update)}", phase='EXECUTION')
        
        # 计算并记录完整的输出状态
        final_state = {**state, **output_update}
        logger.debug(f"输出状态: {logger._format_state(final_state)}", phase='EXECUTION')
        
        logger.info(f"节点 '{node_name}' 执行完成", phase='EXECUTION')
        return output_update
    return wrapper


# --- 7. LangGraph 节点定义 (已集成日志和装饰器) ---

@log_node_execution
def product_researcher_node(state: CompetitorAnalysisState) -> dict:
    logger.search(f"开始研究我们的产品: {state['product_name']}")
    prompt = ChatPromptTemplate.from_template(
        "你是一位产品分析师。请利用搜索工具为产品 '{product_name}' 生成一段100字以内的核心功能和价值主张总结。"
    )
    chain = prompt | model
    summary = chain.invoke({"product_name": state["product_name"]}).content
    logger.result(f"产品总结生成完毕。")
    return {"product_summary": summary}

@log_node_execution
def competitor_finder_node(state: CompetitorAnalysisState) -> dict:
    logger.search(f"开始为 '{state['product_name']}' 寻找 {state['competitor_count']} 个竞品")
    prompt = ChatPromptTemplate.from_template(
        "你是一位市场研究员。请为产品 '{product_name}' 找出 {competitor_count} 个最主要的竞争对手。"
        "利用搜索工具进行查找。请只返回一个用逗号分隔的列表，不要有任何其他文字，例如：'竞品A, 竞品B, 竞品C'。"
    )
    chain = prompt | model
    result = chain.invoke({
        "product_name": state["product_name"],
        "competitor_count": state["competitor_count"]
    }).content
    competitors = [c.strip() for c in result.split(',')]
    logger.result(f"已找到竞品: {competitors}")
    if "research_data" not in state or state["research_data"] is None:
        state["research_data"] = {}
    return {"competitor_list": competitors}

@log_node_execution
def prepare_competitor_analysis_node(state: CompetitorAnalysisState) -> dict:
    """从 competitor_list 中找出下一个要分析的竞品并更新到状态中。"""
    competitor_list = state.get("competitor_list", [])
    research_data = state.get("research_data", {})
    
    next_competitor = None
    for competitor in competitor_list:
        if competitor not in research_data:
            next_competitor = competitor
            break
            
    logger.planning(f"准备分析竞品: '{next_competitor}'")
    return {"competitor_to_analyze": next_competitor}

@log_node_execution
def single_competitor_analyzer_node(state: CompetitorAnalysisState) -> dict:
    competitor_name = state["competitor_to_analyze"]
    logger.search(f"开始深入分析竞品: {competitor_name}")
    
    parser = JsonOutputParser(pydantic_object=CompetitorInfo)
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一位专业的竞品分析师...\n{format_instructions}"),
        ("human", "请为竞品 '{competitor_name}' 进行详细分析...\n**我们的产品**: {product_summary}")
    ])
    chain = prompt | model | parser
    
    try:
        analysis_result = chain.invoke({
            "competitor_name": competitor_name,
            "product_summary": state["product_summary"],
            "format_instructions": parser.get_format_instructions()
        })
        current_data = state.get("research_data", {})
        current_data[competitor_name] = CompetitorInfo.model_validate(analysis_result)
        logger.result(f"对 '{competitor_name}' 的分析已完成。")
        return {"research_data": current_data}
    except Exception as e:
        logger.error(f"分析竞品 '{competitor_name}' 时出错: {e}", phase="EXECUTION")
        return {} # 返回空字典，避免状态污染

@log_node_execution
def report_generator_node(state: CompetitorAnalysisState) -> dict:
    logger.reflection("开始综合所有信息，生成最终报告...")
    parser = JsonOutputParser(pydantic_object=AnalysisReport)
    prompt = ChatPromptTemplate.from_messages([
        ("system", "你是一位资深的AI产品经理...\n{format_instructions}"),
        ("human", "请基于以下数据生成一份全面的竞品分析报告...\n**我们的产品**: {product_name}\n**产品总结**: {product_summary}\n**竞品调研数据**: \n{competitors_json_data}")
    ])
    chain = prompt | model | parser
    competitors_data_list = [v.model_dump() for k, v in state["research_data"].items()]
    
    try:
        report = chain.invoke({
            "product_name": state["product_name"],
            "product_summary": state["product_summary"],
            "competitors_json_data": json.dumps(competitors_data_list, indent=2),
            "format_instructions": parser.get_format_instructions()
        })
        logger.result("最终报告已成功生成。")
        return {"final_report": AnalysisReport.model_validate(report)}
    except Exception as e:
        logger.error(f"生成最终报告时出错: {e}", phase="REFLECTION")
        return {}

# --- 8. LangGraph 边的决策逻辑 ---

def should_continue_analysis(state: CompetitorAnalysisState) -> str:
    """决策：还有未分析的竞品吗？"""
    logger.planning("进入决策点：是否继续分析...")
    competitor_list = state.get("competitor_list", [])
    research_data = state.get("research_data", {})
    
    # 检查是否所有在列表中的竞品都已经被分析（即出现在 research_data 的键中）
    if len(research_data) < len(competitor_list):
        logger.planning("决策：是，还有未分析的竞品。")
        return "continue_analysis"
    else:
        logger.planning("决策：否，所有竞品已分析完毕。")
        return "generate_report"



# --- 9. 图的构建与编译 ---

_app = None

def create_analyzer_agent():
    global _app
    if _app is None:
        logger.info("正在构建和编译AI竞品分析师智能体...", phase="SETUP")
        workflow = StateGraph(CompetitorAnalysisState)
        
        # 添加所有节点，包括新的状态更新节点
        workflow.add_node("product_researcher", product_researcher_node)
        workflow.add_node("competitor_finder", competitor_finder_node)
        workflow.add_node("prepare_for_analysis", prepare_competitor_analysis_node)
        workflow.add_node("single_competitor_analyzer", single_competitor_analyzer_node)
        workflow.add_node("report_generator", report_generator_node)
        
        workflow.set_entry_point("product_researcher")
        workflow.add_edge("product_researcher", "competitor_finder")
        
        # 找到竞品后，进入决策点
        workflow.add_conditional_edges(
            "competitor_finder",
            should_continue_analysis,
            {
                "continue_analysis": "prepare_for_analysis", # 如果继续，先准备
                "generate_report": "report_generator"      # 如果结束，去生成报告
            }
        )
        
        # 准备好之后，去执行单竞品分析
        workflow.add_edge("prepare_for_analysis", "single_competitor_analyzer")
        
        # 分析完一个后，回到 competitor_finder 节点，它会紧接着触发下一次决策
        workflow.add_edge("single_competitor_analyzer", "competitor_finder")
        
        # 报告生成后结束
        workflow.add_edge("report_generator", END)
        
        _app = workflow.compile()
        logger.info("智能体编译完成。", phase="SETUP")
    return _app

# --- 10. 公共API函数 ---

def run_competitor_analysis(product_name: str, competitor_count: int) -> dict:
    global logger
    if logger is None: # 确保在作为库调用时日志器也被初始化
        logger = init_logger()

    app = create_analyzer_agent()
    initial_state = {"product_name": product_name, "competitor_count": competitor_count, "research_data": {}}
    
    logger.info(f"开始为产品 '{product_name}' 进行竞品分析，目标数量: {competitor_count}")
    final_state = None
    # stream() 迭代器本身提供了流程的可视化，我们通过日志器使其更结构化
    for _ in app.stream(initial_state, stream_mode="values"):
        final_state = _
        
    return {"report": final_state.get("final_report")}

# --- 11. 独立运行的入口 ---

def main():
    parser = argparse.ArgumentParser(description="AI竞品分析师智能体命令行工具。")
    parser.add_argument("product_name", default="中医辅助诊疗系统", type=str, help="您自己的产品名称。")

    parser.add_argument("competitor_count", default=3, type=int, help="需要分析的竞品数量。")
    args = parser.parse_args()

    result = run_competitor_analysis(args.product_name, args.competitor_count)
    
    logger.info("="*50, phase="FINAL")
    logger.info("✅ 竞品分析流程结束", phase="FINAL")
    logger.info("="*50, phase="FINAL")
    
    if result["report"]:
        report_json = result["report"].model_dump_json(indent=2)
        logger.result("\n" + report_json)
    else:
        logger.error("未能生成最终报告。", phase="FINAL")

if __name__ == "__main__":
    # 当作为主脚本运行时，main 函数会处理日志初始化
    main()