#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的chains.py和prompts.py文件
验证prompt分离和callback修复是否正常工作
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.prompts import (
        ENHANCED_PLANNING_PROMPT,
        PLANNING_PROMPT,
        COMPETITOR_IDENTIFICATION_PROMPT,
        COMPETITOR_SELECTION_PROMPT,
        KEYWORD_GENERATION_PROMPT,
        CONTENT_RELEVANCE_PROMPT,
        CONTENT_ANALYSIS_PROMPT,
        SUBTASK_EXECUTION_PROMPT,
        VALIDATION_PROMPT,
        FINAL_REPORT_PROMPT
    )
    from aica_agent.chains import (
        planning_chain,
        enhanced_planning_chain,
        competitor_identification_chain,
        competitor_selection_chain,
        keyword_generation_chain,
        content_relevance_chain,
        content_analysis_chain,
        subtask_execution_chain,
        validation_chain,
        final_report_chain
    )
    from aica_agent.utils.callbacks import AICACallbackHandler, PhaseCallbackManager
    from langchain_core.prompts import ChatPromptTemplate
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


class TestPromptSeparation(unittest.TestCase):
    """测试prompt分离是否成功"""
    
    def test_prompts_are_chat_prompt_templates(self):
        """测试所有prompt都是ChatPromptTemplate实例"""
        prompts = [
            ENHANCED_PLANNING_PROMPT,
            PLANNING_PROMPT,
            COMPETITOR_IDENTIFICATION_PROMPT,
            COMPETITOR_SELECTION_PROMPT,
            KEYWORD_GENERATION_PROMPT,
            CONTENT_RELEVANCE_PROMPT,
            CONTENT_ANALYSIS_PROMPT,
            SUBTASK_EXECUTION_PROMPT,
            VALIDATION_PROMPT,
            FINAL_REPORT_PROMPT
        ]
        
        for prompt in prompts:
            with self.subTest(prompt=prompt):
                self.assertIsInstance(prompt, ChatPromptTemplate, 
                                    f"Prompt应该是ChatPromptTemplate实例: {type(prompt)}")
    
    def test_prompts_have_correct_input_variables(self):
        """测试prompt包含正确的输入变量"""
        test_cases = [
            (ENHANCED_PLANNING_PROMPT, ['initial_input', 'search_context']),
            (PLANNING_PROMPT, ['initial_input']),
            (COMPETITOR_IDENTIFICATION_PROMPT, ['query', 'search_results']),
            (COMPETITOR_SELECTION_PROMPT, ['query', 'identified_competitors', 'max_competitors']),
            (VALIDATION_PROMPT, ['conclusion', 'criteria']),
            (FINAL_REPORT_PROMPT, ['analysis_summary'])
        ]
        
        for prompt, expected_vars in test_cases:
            with self.subTest(prompt=prompt):
                actual_vars = prompt.input_variables
                for var in expected_vars:
                    self.assertIn(var, actual_vars, 
                                f"Prompt应该包含变量 {var}, 实际变量: {actual_vars}")


class TestChainCreation(unittest.TestCase):
    """测试chain创建是否正常"""
    
    def test_chains_exist(self):
        """测试所有chain都存在"""
        chains = [
            planning_chain,
            enhanced_planning_chain,
            competitor_identification_chain,
            competitor_selection_chain,
            keyword_generation_chain,
            content_relevance_chain,
            content_analysis_chain,
            subtask_execution_chain,
            validation_chain,
            final_report_chain
        ]
        
        for chain in chains:
            with self.subTest(chain=chain):
                self.assertIsNotNone(chain, "Chain不应该为None")
    
    def test_chain_structure(self):
        """测试chain结构正确"""
        # 测试planning_chain包含正确的prompt
        self.assertTrue(hasattr(planning_chain, 'first'), "planning_chain应该有first属性")
        
        # 测试enhanced_planning_chain包含正确的prompt
        self.assertTrue(hasattr(enhanced_planning_chain, 'first'), "enhanced_planning_chain应该有first属性")


class TestCallbackHandler(unittest.TestCase):
    """测试callback处理器的修复"""
    
    def setUp(self):
        self.callback = AICACallbackHandler("TEST")
    
    def test_keyboard_interrupt_handling(self):
        """测试KeyboardInterrupt处理是否正确"""
        # 测试on_llm_error
        with self.assertRaises(KeyboardInterrupt):
            self.callback.on_llm_error(KeyboardInterrupt("用户中断"))
        
        # 测试on_chain_error
        with self.assertRaises(KeyboardInterrupt):
            self.callback.on_chain_error(KeyboardInterrupt("用户中断"))
        
        # 测试on_tool_error
        with self.assertRaises(KeyboardInterrupt):
            self.callback.on_tool_error(KeyboardInterrupt("用户中断"))
    
    def test_normal_exception_handling(self):
        """测试普通异常处理不会抛出"""
        # 这些调用不应该抛出异常
        try:
            self.callback.on_llm_error(ValueError("测试错误"))
            self.callback.on_chain_error(RuntimeError("测试错误"))
            self.callback.on_tool_error(Exception("测试错误"))
        except Exception as e:
            self.fail(f"普通异常处理不应该抛出异常: {e}")
    
    def test_phase_callback_manager(self):
        """测试阶段回调管理器"""
        callbacks = PhaseCallbackManager.get_planning_callbacks()
        self.assertIsInstance(callbacks, list)
        self.assertTrue(len(callbacks) > 0)
        self.assertIsInstance(callbacks[0], AICACallbackHandler)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    @patch('aica_agent.chains.llm')
    def test_prompt_formatting(self, mock_llm):
        """测试prompt格式化是否正常"""
        # 模拟LLM响应
        mock_response = Mock()
        mock_response.content = '{"competitors_to_research": ["测试竞品"], "sub_tasks": []}'
        mock_llm.with_structured_output.return_value.invoke.return_value = mock_response
        
        # 测试planning prompt格式化
        try:
            formatted = PLANNING_PROMPT.format(initial_input="测试输入")
            self.assertIn("测试输入", formatted)
        except Exception as e:
            self.fail(f"Prompt格式化失败: {e}")
    
    def test_callback_integration(self):
        """测试callback集成"""
        callback = AICACallbackHandler("INTEGRATION_TEST")
        
        # 测试基本回调方法
        try:
            callback.on_llm_start({}, ["测试prompt"])
            callback.on_text("测试文本")
            callback.on_llm_end(Mock(generations=[[Mock(text="测试响应")]], llm_output={}))
        except Exception as e:
            self.fail(f"Callback集成测试失败: {e}")


def run_tests():
    """运行所有测试"""
    print("=" * 60)
    print("开始运行重构后代码的测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestPromptSeparation))
    suite.addTests(loader.loadTestsFromTestCase(TestChainCreation))
    suite.addTests(loader.loadTestsFromTestCase(TestCallbackHandler))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n测试{'成功' if success else '失败'}!")
    print("=" * 60)
    
    return success


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
