# aica_agent/enhanced_graph.py - 增强版图结构
import os
from datetime import datetime
from langgraph.graph import StateGraph, END
from .state import AgentState
from .enhanced_nodes import (
    competitor_confirmation_node,
    detailed_research_node,
)
from .nodes import (
    select_next_competitor_node,
    select_next_sub_task_node,
    subtask_execution_node,
    reflection_node,
    final_report_node,
)
from config import MAX_SUBTASK_RETRIES
from ..utils.logger import get_logger

def should_proceed_to_detailed_research(state: AgentState) -> str:
    """条件: 是否进入详细研究阶段"""
    if state.selected_competitors and len(state.selected_competitors) > 0:
        return "proceed_to_research"
    else:
        return "skip_research"

def should_proceed_to_traditional_analysis(state: AgentState) -> str:
    """条件: 是否进入传统分析流程"""
    if state.competitor_research and len(state.competitor_research) > 0:
        return "proceed_to_analysis"
    else:
        return "end_process"

def should_start_next_competitor(state: AgentState) -> str:
    """条件: 是否还有下一个竞品需要分析"""
    if state.current_competitor_name is None:
        return "yes_all_done" # 所有竞品都完成了
    return "no_more_competitors" # 开始分析当前竞品

def should_start_next_sub_task(state: AgentState) -> str:
    """条件: 当前竞品是否还有下一个子任务"""
    if state.current_sub_task_name is None:
        return "yes_sub_tasks_done" # 当前竞品的所有子任务完成
    return "no_more_sub_tasks" # 开始分析当前子任务

def did_sub_task_succeed(state: AgentState) -> str:
    """条件: 子任务是否通过验证，或是否已达最大重试次数"""
    logger = get_logger()
    is_complete = False
    for comp in state.analysis_plan:
        if comp.competitor_name == state.current_competitor_name:
            for task in comp.sub_tasks:
                if task.task_name == state.current_sub_task_name:
                    is_complete = task.is_complete
                    break
            break
            
    if is_complete:
        return "success"
    elif state.current_retry_count >= MAX_SUBTASK_RETRIES:
        logger.warning(f"子任务 '{state.current_sub_task_name}' 已达最大重试次数，强制继续", "EXECUTION")
        return "max_retries_reached"
    else:
        return "retry"

def create_enhanced_report_node(state: AgentState) -> dict:
    """增强版报告生成节点，整合详细研究结果"""
    logger = get_logger()
    logger.result("=" * 60)
    logger.result("开始生成增强版最终报告")
    logger.result("=" * 60)
    
    # 整合详细研究结果
    enhanced_summary = ""
    
    # 添加竞品确认阶段的结果
    if state.selected_competitors:
        enhanced_summary += "# 竞品确认阶段结果\n\n"
        enhanced_summary += f"## 选定的深度研究竞品 ({len(state.selected_competitors)}个)\n\n"
        
        for i, competitor in enumerate(state.selected_competitors):
            enhanced_summary += f"### {i+1}. {competitor.name}\n"
            enhanced_summary += f"- **所属公司**: {competitor.company}\n"
            enhanced_summary += f"- **主要功能**: {', '.join(competitor.main_functions)}\n"
            enhanced_summary += f"- **产品描述**: {competitor.description}\n"
            enhanced_summary += f"- **相关性评分**: {competitor.relevance_score}\n\n"
    
    # 添加详细研究结果
    if state.competitor_research:
        enhanced_summary += "# 详细研究阶段结果\n\n"
        
        for research in state.competitor_research:
            enhanced_summary += f"## {research.competitor_info.name} 深度分析\n\n"
            enhanced_summary += f"**搜索关键词**: {', '.join(research.search_keywords)}\n\n"
            enhanced_summary += f"**收集内容数量**: {len(research.collected_contents)} 个网页\n\n"
            enhanced_summary += research.analysis_summary + "\n\n"
    
    # 添加传统分析结果（如果有）
    if state.analysis_plan:
        enhanced_summary += "# 传统分析阶段结果\n\n"
        for comp_analysis in state.analysis_plan:
            enhanced_summary += f"## 竞品分析: {comp_analysis.competitor_name}\n\n"
            for task in comp_analysis.sub_tasks:
                enhanced_summary += f"### {task.task_name}\n"
                enhanced_summary += f"{task.result}\n\n"
    
    # 生成最终报告
    from .chains import final_report_chain
    from ..utils.callbacks import PhaseCallbackManager
    
    callbacks = PhaseCallbackManager.get_reporting_callbacks()
    report_markdown = final_report_chain.invoke(
        {"analysis_summary": enhanced_summary},
        config={"callbacks": callbacks}
    ).content
    
    # 保存增强版报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    docs_dir = "docs"
    os.makedirs(docs_dir, exist_ok=True)
    markdown_filepath = os.path.join(docs_dir, f"enhanced_competitive_analysis_report_{timestamp}.md")
    
    with open(markdown_filepath, 'w', encoding='utf-8') as f:
        f.write(report_markdown)
    
    logger.result(f"✅ 增强版最终报告已生成: {markdown_filepath}")
    logger.result(f"报告长度: {len(report_markdown)} 字符")
    
    # 记录输入输出
    logger.log_input_output(
        "ENHANCED_REPORTING",
        {
            "selected_competitors_count": len(state.selected_competitors) if state.selected_competitors else 0,
            "research_results_count": len(state.competitor_research) if state.competitor_research else 0,
            "enhanced_summary_length": len(enhanced_summary)
        },
        {
            "report_markdown_length": len(report_markdown),
            "filepath": markdown_filepath
        }
    )
    
    return {
        "final_report_markdown": report_markdown,
        "report_filepath": markdown_filepath
    }

def build_enhanced_graph():
    """构建增强版的 Agent 图"""
    logger = get_logger()
    logger.info("构建增强版 Agent 图")
    
    workflow = StateGraph(AgentState)

    # 添加所有节点
    workflow.add_node("competitor_confirmation", competitor_confirmation_node)
    workflow.add_node("detailed_research", detailed_research_node)
    workflow.add_node("enhanced_reporter", create_enhanced_report_node)
    
    # 传统分析节点（保留用于兼容）
    workflow.add_node("next_competitor_selector", select_next_competitor_node)
    workflow.add_node("next_sub_task_selector", select_next_sub_task_node)
    workflow.add_node("executor", subtask_execution_node)
    workflow.add_node("reflector", reflection_node)
    workflow.add_node("traditional_reporter", final_report_node)

    # --- 设定图的入口和边的连接 ---
    workflow.set_entry_point("competitor_confirmation")
    
    # 竞品确认 -> 详细研究
    workflow.add_conditional_edges(
        "competitor_confirmation",
        should_proceed_to_detailed_research,
        {
            "proceed_to_research": "detailed_research",
            "skip_research": "enhanced_reporter"
        }
    )
    
    # 详细研究 -> 报告生成
    workflow.add_conditional_edges(
        "detailed_research",
        should_proceed_to_traditional_analysis,
        {
            "proceed_to_analysis": "enhanced_reporter",
            "end_process": "enhanced_reporter"
        }
    )
    
    # 报告生成后结束
    workflow.add_edge("enhanced_reporter", END)
    
    logger.info("增强版 Agent 图构建完成")
    return workflow.compile()

def build_traditional_graph():
    """构建传统版的 Agent 图（保持向后兼容）"""
    logger = get_logger()
    logger.info("构建传统版 Agent 图")
    
    workflow = StateGraph(AgentState)

    # 添加传统节点
    from .nodes import planning_node
    workflow.add_node("planner", planning_node)
    workflow.add_node("next_competitor_selector", select_next_competitor_node)
    workflow.add_node("next_sub_task_selector", select_next_sub_task_node)
    workflow.add_node("executor", subtask_execution_node)
    workflow.add_node("reflector", reflection_node)
    workflow.add_node("reporter", final_report_node)

    # --- 设定图的入口和边的连接 ---
    workflow.set_entry_point("planner")
    
    # 规划完成后，选择第一个竞品
    workflow.add_edge("planner", "next_competitor_selector")

    # 外层循环：竞品分析
    workflow.add_conditional_edges(
        "next_competitor_selector",
        should_start_next_competitor,
        {
            "yes_all_done": "reporter", # 所有竞品完成，去生成报告
            "no_more_competitors": "next_sub_task_selector" # 开始分析这个竞品
        }
    )

    # 内层循环：子任务分析
    workflow.add_conditional_edges(
        "next_sub_task_selector",
        should_start_next_sub_task,
        {
            "yes_sub_tasks_done": "next_competitor_selector", # 子任务完成，去选下一个竞品
            "no_more_sub_tasks": "executor" # 开始执行这个子任务
        }
    )
    
    # 行动-反思循环
    workflow.add_edge("executor", "reflector")
    workflow.add_conditional_edges(
        "reflector",
        did_sub_task_succeed,
        {
            "success": "next_sub_task_selector", # 成功，去选下一个子任务
            "max_retries_reached": "next_sub_task_selector", # 达到最大重试，也继续
            "retry": "executor" # 失败，重试执行
        }
    )
    
    # 报告生成后结束
    workflow.add_edge("reporter", END)
    
    logger.info("传统版 Agent 图构建完成")
    return workflow.compile()

# 默认使用增强版图
def build_graph():
    """构建默认的 Agent 图（增强版）"""
    return build_enhanced_graph()

def run_aica_analysis(topic: str, mode: str = "enhanced") -> dict:
    """
    运行AICA分析的包装函数，用于main.py调用

    Args:
        topic: 分析主题
        mode: 分析模式 ("enhanced" 或 "traditional")

    Returns:
        分析结果字典
    """
    logger = get_logger()

    try:
        if mode == "enhanced":
            app = build_enhanced_graph()
        else:
            app = build_traditional_graph()

        logger.info(f"选择了 {mode} 模式")
        logger.info(f"分析主题: {topic}")

        # 定义初始状态
        initial_state = {"initial_input": topic}

        print(f"\n开始执行竞品分析 ({mode} 模式)")
        logger.info(f"开始执行竞品分析 ({mode} 模式)")

        # 流式执行图
        final_state = None
        for s in app.stream(initial_state, {"recursion_limit": 150}):
            node_name = list(s.keys())[0]
            node_output = s[node_name]
            final_state = s

            print(f"[COMPLETE] 节点 '{node_name}' 执行完毕")
            logger.info(f"节点 '{node_name}' 执行完毕")

        print("\n[SUCCESS] 分析完成！")
        logger.info("AICA 分析执行完成")

        return {"status": "success", "final_state": final_state}

    except Exception as e:
        logger.error(f"AICA 分析执行错误: {e}")
        return {"status": "error", "error": str(e)}
