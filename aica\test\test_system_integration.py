#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成测试 - 测试重构后的系统是否能正常运行
"""

import sys
import os
import signal
import time
from threading import Thread

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_keyboard_interrupt():
    """测试Ctrl+C中断功能"""
    print("测试Ctrl+C中断功能...")
    
    try:
        from aica_agent.chains import planning_chain
        from aica_agent.utils.callbacks import PhaseCallbackManager
        
        # 创建一个会被中断的任务
        def interrupt_task():
            time.sleep(2)  # 等待2秒后发送中断信号
            os.kill(os.getpid(), signal.SIGINT)
        
        # 启动中断线程
        interrupt_thread = Thread(target=interrupt_task)
        interrupt_thread.daemon = True
        interrupt_thread.start()
        
        # 尝试执行一个长时间运行的任务
        print("开始执行任务（将在2秒后被中断）...")
        start_time = time.time()
        
        try:
            # 模拟长时间运行的任务
            for i in range(100):
                time.sleep(0.1)  # 模拟工作
                if i % 10 == 0:
                    print(f"工作进度: {i}%")
        except KeyboardInterrupt:
            elapsed = time.time() - start_time
            print(f"✓ 成功捕获KeyboardInterrupt，耗时: {elapsed:.2f}秒")
            return True
        
        print("✗ 未能捕获KeyboardInterrupt")
        return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_prompt_chain_integration():
    """测试prompt和chain的集成"""
    print("\n测试prompt和chain的集成...")
    
    try:
        from aica_agent.prompts import PLANNING_PROMPT
        from aica_agent.chains import planning_chain
        
        # 测试prompt格式化
        test_input = "分析微信小程序的竞品"
        formatted_prompt = PLANNING_PROMPT.format(initial_input=test_input)
        
        print(f"✓ Prompt格式化成功")
        print(f"  输入: {test_input}")
        print(f"  格式化后长度: {len(formatted_prompt)} 字符")
        
        # 测试chain结构
        if hasattr(planning_chain, 'first'):
            print("✓ Chain结构正确")
        else:
            print("✗ Chain结构异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        return False


def test_callback_functionality():
    """测试callback功能"""
    print("\n测试callback功能...")
    
    try:
        from aica_agent.utils.callbacks import AICACallbackHandler, PhaseCallbackManager
        
        # 测试callback创建
        callback = AICACallbackHandler("SYSTEM_TEST")
        print("✓ Callback创建成功")
        
        # 测试阶段管理器
        callbacks = PhaseCallbackManager.get_planning_callbacks()
        if len(callbacks) > 0:
            print("✓ 阶段回调管理器正常")
        else:
            print("✗ 阶段回调管理器异常")
            return False
        
        # 测试KeyboardInterrupt处理
        try:
            callback.on_llm_error(KeyboardInterrupt("测试中断"))
            print("✗ KeyboardInterrupt未被正确抛出")
            return False
        except KeyboardInterrupt:
            print("✓ KeyboardInterrupt处理正确")
        
        return True
        
    except Exception as e:
        print(f"✗ Callback测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("AICA系统集成测试")
    print("=" * 60)
    
    tests = [
        ("Prompt和Chain集成", test_prompt_chain_integration),
        ("Callback功能", test_callback_functionality),
        ("Ctrl+C中断功能", test_keyboard_interrupt),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统重构成功！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✓ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
