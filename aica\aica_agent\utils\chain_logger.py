# aica_agent/utils/chain_logger.py
"""
Chain日志记录工具 - 替代callback功能
提供可运行的对象函数来记录chain的输入输出信息
"""

from typing import Any, Dict, Union
from langchain_core.runnables import RunnableLambda
from .logger import get_logger
import json
import time
import threading
import signal


class ChainLogger:
    """Chain日志记录器"""

    def __init__(self, chain_name: str, phase: str = "UNKNOWN"):
        self.chain_name = chain_name
        self.phase = phase
        self.logger = get_logger()
        self.start_time = None
        self.heartbeat_timer = None
    
    def log_input(self, inputs: Any) -> Any:
        """记录chain输入"""
        try:
            self.start_time = time.time()
            self.logger.info(f"[{self.phase}] [CHAIN_START] 开始执行Chain: {self.chain_name}", self.phase)
            self.logger.info(f"[{self.phase}] [TIMESTAMP] 开始时间: {time.strftime('%H:%M:%S', time.localtime(self.start_time))}", self.phase)

            # 启动心跳监控
            self._start_heartbeat()

            # 安全处理输入数据
            if isinstance(inputs, dict):
                safe_inputs = self._sanitize_data(inputs)
                self.logger.debug(f"[{self.phase}] [INPUT] Chain输入: {safe_inputs}", self.phase)
            else:
                input_str = str(inputs)[:500] + "..." if len(str(inputs)) > 500 else str(inputs)
                self.logger.debug(f"[{self.phase}] [INPUT] Chain输入: {input_str}", self.phase)

            return inputs  # 透传输入
        except Exception as e:
            self.logger.error(f"[{self.phase}] [ERROR] 记录Chain输入失败: {e}", self.phase)
            return inputs
    
    def log_output(self, outputs: Any) -> Any:
        """记录chain输出"""
        try:
            # 停止心跳监控
            self._stop_heartbeat()

            end_time = time.time()
            if self.start_time:
                duration = end_time - self.start_time
                self.logger.info(f"[{self.phase}] [CHAIN_END] 完成执行Chain: {self.chain_name}", self.phase)
                self.logger.info(f"[{self.phase}] [DURATION] 执行耗时: {duration:.2f}秒", self.phase)
            else:
                self.logger.info(f"[{self.phase}] [CHAIN_END] 完成执行Chain: {self.chain_name}", self.phase)

            # 安全处理输出数据
            if isinstance(outputs, dict):
                safe_outputs = self._sanitize_data(outputs)
                self.logger.debug(f"[{self.phase}] [OUTPUT] Chain输出: {safe_outputs}", self.phase)
            elif hasattr(outputs, '__dict__'):
                # 处理Pydantic模型等对象
                try:
                    if hasattr(outputs, 'dict'):
                        output_dict = outputs.dict()
                        safe_outputs = self._sanitize_data(output_dict)
                        self.logger.debug(f"[{self.phase}] [OUTPUT] Chain输出: {safe_outputs}", self.phase)
                    else:
                        output_str = str(outputs)[:500] + "..." if len(str(outputs)) > 500 else str(outputs)
                        self.logger.debug(f"[{self.phase}] [OUTPUT] Chain输出: {output_str}", self.phase)
                except:
                    output_str = str(outputs)[:500] + "..." if len(str(outputs)) > 500 else str(outputs)
                    self.logger.debug(f"[{self.phase}] [OUTPUT] Chain输出: {output_str}", self.phase)
            else:
                output_str = str(outputs)[:500] + "..." if len(str(outputs)) > 500 else str(outputs)
                self.logger.debug(f"[{self.phase}] [OUTPUT] Chain输出: {output_str}", self.phase)

            return outputs  # 透传输出
        except Exception as e:
            self.logger.error(f"[{self.phase}] [ERROR] 记录Chain输出失败: {e}", self.phase)
            return outputs
    
    def log_error(self, error: Exception) -> None:
        """记录chain错误"""
        try:
            if isinstance(error, KeyboardInterrupt):
                self.logger.info(f"[{self.phase}] [INTERRUPTED] Chain被用户中断: {self.chain_name}", self.phase)
                raise error  # 重新抛出KeyboardInterrupt
            else:
                self.logger.error(f"[{self.phase}] [ERROR] Chain执行失败: {self.chain_name}, 错误: {error}", self.phase)
        except KeyboardInterrupt:
            raise
        except Exception as log_error:
            print(f"日志记录失败: {log_error}")
    
    def _sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理数据，避免记录过长信息"""
        sanitized = {}
        for key, value in data.items():
            if isinstance(value, str):
                if len(value) > 300:
                    sanitized[key] = value[:300] + f"... (截断，原长度: {len(value)})"
                else:
                    sanitized[key] = value
            elif isinstance(value, (list, dict)):
                sanitized[key] = f"<{type(value).__name__} 对象，长度: {len(value)}>"
            else:
                sanitized[key] = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
        return sanitized

    def _start_heartbeat(self):
        """启动心跳监控"""
        self._stop_heartbeat()  # 确保没有重复的定时器
        self.heartbeat_timer = threading.Timer(10.0, self._heartbeat)
        self.heartbeat_timer.daemon = True
        self.heartbeat_timer.start()

    def _stop_heartbeat(self):
        """停止心跳监控"""
        if self.heartbeat_timer:
            self.heartbeat_timer.cancel()
            self.heartbeat_timer = None

    def _heartbeat(self):
        """心跳监控，每10秒输出一次状态"""
        try:
            if self.start_time:
                elapsed = time.time() - self.start_time
                self.logger.info(f"[{self.phase}] [HEARTBEAT] Chain仍在执行: {self.chain_name}, 已耗时: {elapsed:.1f}秒", self.phase)

                # 如果超过60秒，发出警告
                if elapsed > 60:
                    self.logger.warning(f"[{self.phase}] [WARNING] Chain执行时间过长: {self.chain_name}, 已耗时: {elapsed:.1f}秒", self.phase)

                # 继续下一次心跳
                self.heartbeat_timer = threading.Timer(10.0, self._heartbeat)
                self.heartbeat_timer.daemon = True
                self.heartbeat_timer.start()
        except Exception as e:
            self.logger.error(f"[{self.phase}] [ERROR] 心跳监控失败: {e}", self.phase)


def create_logging_chain(chain, chain_name: str, phase: str = "UNKNOWN"):
    """
    创建带日志记录的chain - 简化版本，避免干扰LLM structured output

    Args:
        chain: 原始的chain对象
        chain_name: chain的名称
        phase: 执行阶段名称

    Returns:
        带日志记录功能的chain
    """
    logger = get_logger()

    def logged_invoke(inputs):
        try:
            # 记录开始执行
            logger.info(f"[{phase}] [CHAIN_START] 开始执行Chain: {chain_name}", phase)

            # 记录输入（简化）
            if isinstance(inputs, dict):
                input_keys = list(inputs.keys())
                logger.debug(f"[{phase}] [INPUT] Chain输入键: {input_keys}", phase)
            else:
                logger.debug(f"[{phase}] [INPUT] Chain输入类型: {type(inputs).__name__}", phase)

            # 执行原始chain
            result = chain.invoke(inputs)

            # 记录输出（简化）
            if hasattr(result, '__dict__'):
                logger.debug(f"[{phase}] [OUTPUT] Chain输出类型: {type(result).__name__}", phase)
            else:
                logger.debug(f"[{phase}] [OUTPUT] Chain输出类型: {type(result).__name__}", phase)

            # 记录完成
            logger.info(f"[{phase}] [CHAIN_END] 完成执行Chain: {chain_name}", phase)

            return result

        except KeyboardInterrupt:
            logger.info(f"[{phase}] [INTERRUPTED] Chain被用户中断: {chain_name}", phase)
            raise
        except Exception as e:
            logger.error(f"[{phase}] [ERROR] Chain执行失败: {chain_name}, 错误: {e}", phase)
            raise

    # 创建新的RunnableLambda
    return RunnableLambda(logged_invoke, name=chain_name)


def log_function_call(func_name: str, phase: str = "UNKNOWN"):
    """
    装饰器：记录函数调用
    
    Args:
        func_name: 函数名称
        phase: 执行阶段
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger()
            try:
                logger.info(f"[{phase}] [FUNC_START] 开始执行函数: {func_name}", phase)
                result = func(*args, **kwargs)
                logger.info(f"[{phase}] [FUNC_END] 完成执行函数: {func_name}", phase)
                return result
            except KeyboardInterrupt:
                logger.info(f"[{phase}] [INTERRUPTED] 函数被用户中断: {func_name}", phase)
                raise
            except Exception as e:
                logger.error(f"[{phase}] [ERROR] 函数执行失败: {func_name}, 错误: {e}", phase)
                raise
        return wrapper
    return decorator


# 便捷函数
def create_planning_chain_with_logging(chain, enhanced=False):
    """创建带日志的规划chain"""
    chain_name = "enhanced_planning_chain" if enhanced else "planning_chain"
    return create_logging_chain(chain, chain_name, "PLANNING")


def create_competitor_identification_chain_with_logging(chain):
    """创建带日志的竞品识别chain"""
    return create_logging_chain(chain, "competitor_identification_chain", "COMPETITOR_CONFIRMATION")


def create_competitor_selection_chain_with_logging(chain):
    """创建带日志的竞品选择chain"""
    return create_logging_chain(chain, "competitor_selection_chain", "COMPETITOR_CONFIRMATION")


def create_keyword_generation_chain_with_logging(chain):
    """创建带日志的关键词生成chain"""
    return create_logging_chain(chain, "keyword_generation_chain", "DETAILED_RESEARCH")


def create_content_relevance_chain_with_logging(chain):
    """创建带日志的内容相关性chain"""
    return create_logging_chain(chain, "content_relevance_chain", "DETAILED_RESEARCH")


def create_content_analysis_chain_with_logging(chain):
    """创建带日志的内容分析chain"""
    return create_logging_chain(chain, "content_analysis_chain", "DETAILED_RESEARCH")


def create_subtask_execution_chain_with_logging(chain):
    """创建带日志的子任务执行chain"""
    return create_logging_chain(chain, "subtask_execution_chain", "EXECUTION")


def create_validation_chain_with_logging(chain):
    """创建带日志的验证chain"""
    return create_logging_chain(chain, "validation_chain", "REFLECTION")


def create_final_report_chain_with_logging(chain):
    """创建带日志的最终报告chain"""
    return create_logging_chain(chain, "final_report_chain", "REPORTING")
