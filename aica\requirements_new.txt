# AICA Project Dependencies
# Install command: pip install -r requirements.txt

# Core frameworks
langgraph>=0.2.0
langchain>=0.3.0
langchain-community>=0.3.0
langchain-ollama>=0.2.0
langchain-openai>=0.2.0

# Data processing
pydantic>=2.0.0

# Network tools
ddgs>=6.0.0
requests>=2.31.0

# Logging and terminal
colorama>=0.4.6

# Document processing
reportlab>=4.0.0

# Development tools
python-dotenv>=1.0.0
