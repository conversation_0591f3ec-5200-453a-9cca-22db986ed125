# AICA 测试文件说明

## 核心测试文件

### 1. 基础功能测试
- **test_basic_functionality.py** - 基础功能测试，验证核心组件
- **test_ollama_basic.py** - Ollama模型基础功能测试

### 2. 完整工作流测试
- **test_complete_workflow.py** - 完整竞品分析工作流测试
- **test_system_integration.py** - 系统集成测试

### 3. 模型兼容性测试
- **test_qwen_fix.py** - 阿里云qwen模型兼容性测试
- **test_qwen_complete.py** - qwen模型完整功能测试
- **test_unicode_fix.py** - Unicode编码问题修复验证

### 4. 系统增强功能测试
- **test_chain_logger.py** - 日志记录系统测试
- **test_simple_timeout.py** - 超时控制和心跳监控测试

### 5. 开发调试
- **test.ipynb** - Jupyter notebook，用于交互式测试和调试

## 运行测试

### 环境准备
```bash
# 激活conda环境
conda activate llms

# 进入项目目录
cd aica
```

### 运行单个测试
```bash
# 基础功能测试
& D:/ProgramData/Miniconda3/envs/llms/python.exe test/test_basic_functionality.py

# 完整工作流测试
& D:/ProgramData/Miniconda3/envs/llms/python.exe test/test_complete_workflow.py

# 超时控制测试
& D:/ProgramData/Miniconda3/envs/llms/python.exe test/test_simple_timeout.py
```

### 测试覆盖范围

1. **基础组件**: 模型连接、chain构建、数据结构
2. **核心功能**: 竞品识别、任务生成、内容分析
3. **系统增强**: 日志记录、超时控制、错误处理
4. **模型兼容**: ollama、qwen等不同模型支持
5. **编码兼容**: Windows GBK环境Unicode处理

## 测试文件命名规范

- `test_basic_*` - 基础功能测试
- `test_*_fix` - 问题修复验证测试
- `test_complete_*` - 完整功能测试
- `test_system_*` - 系统级测试

## 注意事项

1. 测试需要在llms conda环境中运行
2. 确保.env文件配置了必要的API密钥
3. 某些测试需要网络连接（搜索功能）
4. 日志文件会自动生成在logs目录中
