#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 智能体系统主入口

支持多个智能体的选择和运行，具有良好的可扩展性。
"""

import os
import sys
import importlib
from typing import Dict, Callable, Any
from agents.utils.logger import get_logger

# 智能体注册表
AGENT_REGISTRY: Dict[str, Dict[str, Any]] = {
    "task_planner": {
        "name": "任务规划智能体",
        "description": "将高层次目标分解为具体的可执行任务",
        "module": "agents.task_planner",
        "function": "run_task_planner",
        "params": {
            "goal": {"type": "str", "prompt": "请输入需要规划的目标：", "required": True},
            "verbose": {"type": "bool", "prompt": "是否显示详细过程？(y/n)：", "default": True}
        }
    },
    "comp_analyzer": {
        "name": "竞品分析智能体",
        "description": "深度分析竞争对手，生成专业的竞品分析报告",
        "module": "agents.comp_analyzer.agent",
        "function": "run_competitor_analysis",
        "params": {
            "product_name": {"type": "str", "prompt": "请输入您的产品名称：", "required": True},
            "competitor_count": {"type": "int", "prompt": "需要额外调研的竞品数量 (默认3)：", "default": 3},
            "known_competitors": {"type": "list", "prompt": "已知竞品列表 (用逗号分隔，可选)：", "default": []},
            "focus_features": {"type": "list", "prompt": "重点关注的功能点 (用逗号分隔，可选)：", "default": []}
        }
    },
    "aica_agent": {
        "name": "AICA竞品分析智能体 (传统版)",
        "description": "传统的竞品分析智能体，支持增强模式和传统模式",
        "module": "agents.aica_agent.enhanced_graph",
        "function": "run_aica_analysis",
        "params": {
            "topic": {"type": "str", "prompt": "请输入您想分析的产品、公司或领域：", "required": True},
            "mode": {"type": "choice", "prompt": "选择分析模式", "choices": ["enhanced", "traditional"], "default": "enhanced"}
        }
    }
}

def get_user_input(param_name: str, param_config: Dict[str, Any]) -> Any:
    """获取用户输入并转换为正确的类型"""
    prompt = param_config.get("prompt", f"请输入 {param_name}：")
    param_type = param_config.get("type", "str")
    default = param_config.get("default")
    required = param_config.get("required", False)

    while True:
        if param_type == "choice":
            choices = param_config.get("choices", [])
            print(f"\n{prompt}")
            for i, choice in enumerate(choices, 1):
                print(f"  {i}. {choice}")

            try:
                choice_input = input(f"请选择 (1-{len(choices)})：").strip()
                if not choice_input and default is not None:
                    return default
                choice_idx = int(choice_input) - 1
                if 0 <= choice_idx < len(choices):
                    return choices[choice_idx]
                else:
                    print("无效选择，请重新输入")
                    continue
            except ValueError:
                print("请输入有效的数字")
                continue

        elif param_type == "bool":
            user_input = input(f"\n{prompt}").strip().lower()
            if not user_input and default is not None:
                return default
            if user_input in ['y', 'yes', 'true', '1']:
                return True
            elif user_input in ['n', 'no', 'false', '0']:
                return False
            else:
                print("请输入 y/n")
                continue

        else:
            user_input = input(f"\n{prompt}").strip()

            if not user_input:
                if default is not None:
                    return default
                elif not required:
                    return None if param_type != "list" else []
                else:
                    print("此参数为必填项，请输入值")
                    continue

            if param_type == "int":
                try:
                    return int(user_input)
                except ValueError:
                    print("请输入有效的整数")
                    continue
            elif param_type == "list":
                if not user_input:
                    return []
                return [item.strip() for item in user_input.split(',') if item.strip()]
            else:  # str
                return user_input

def run_agent(agent_key: str, agent_config: Dict[str, Any]) -> bool:
    """运行指定的智能体"""
    logger = get_logger()

    try:
        print(f"\n{'='*60}")
        print(f"启动智能体: {agent_config['name']}")
        print(f"描述: {agent_config['description']}")
        print(f"{'='*60}")

        # 收集参数
        params = {}
        for param_name, param_config in agent_config.get("params", {}).items():
            params[param_name] = get_user_input(param_name, param_config)

        # 动态导入模块
        module = importlib.import_module(agent_config["module"])
        function = getattr(module, agent_config["function"])

        print(f"\n开始执行智能体...")
        logger.info(f"开始执行智能体: {agent_config['name']}")
        logger.info(f"参数: {params}")

        # 执行智能体
        result = function(**params)

        print(f"\n✅ 智能体执行完成！")
        logger.info("智能体执行完成")

        if result:
            logger.info(f"执行结果: {result}")

        return True

    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断执行")
        logger.warning("用户中断执行")
        return False
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        logger.error(f"执行过程中出现错误: {e}")
        return False

def show_agent_menu():
    """显示智能体选择菜单"""
    print("\n" + "="*60)
    print("AICA 智能体系统")
    print("="*60)
    print("可用的智能体:")

    for i, (key, config) in enumerate(AGENT_REGISTRY.items(), 1):
        print(f"  {i}. {config['name']}")
        print(f"     {config['description']}")

    print(f"  {len(AGENT_REGISTRY) + 1}. 退出系统")
    print("="*60)

def main():
    """主程序入口"""
    logger = get_logger()

    # 确保必要目录存在
    os.makedirs("logs", exist_ok=True)
    os.makedirs("docs", exist_ok=True)

    logger.info("AICA 智能体系统启动")

    while True:
        show_agent_menu()

        try:
            choice = input(f"\n请选择要运行的智能体 (1-{len(AGENT_REGISTRY) + 1})：").strip()

            if not choice:
                continue

            choice_num = int(choice)

            if choice_num == len(AGENT_REGISTRY) + 1:
                print("\n👋 感谢使用 AICA 智能体系统，再见！")
                logger.info("用户退出系统")
                break

            if 1 <= choice_num <= len(AGENT_REGISTRY):
                agent_keys = list(AGENT_REGISTRY.keys())
                selected_agent_key = agent_keys[choice_num - 1]
                selected_agent_config = AGENT_REGISTRY[selected_agent_key]

                success = run_agent(selected_agent_key, selected_agent_config)

                if success:
                    input("\n按回车键继续...")
                else:
                    input("\n按回车键返回主菜单...")
            else:
                print("无效选择，请重新输入")

        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n\n👋 感谢使用 AICA 智能体系统，再见！")
            logger.info("用户中断退出系统")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            logger.error(f"主程序错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()