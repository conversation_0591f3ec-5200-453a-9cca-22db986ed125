import os
import sys
from aica_agent.enhanced_graph import build_enhanced_graph, build_traditional_graph
from config import REPORTS_DIR
from aica_agent.utils.logger import init_logger, get_logger

def main():
    """主程序入口"""
    # 初始化日志系统
    logger = init_logger("logs")

    # 确保报告输出目录存在
    os.makedirs(REPORTS_DIR, exist_ok=True)
    os.makedirs("docs", exist_ok=True)

    # 选择分析模式
    print("🤖 AICA 智能竞品分析系统")
    print("=" * 50)
    print("请选择分析模式:")
    print("1. 增强模式 (推荐) - 深度竞品确认 + 详细资料搜寻")
    print("2. 传统模式 - 基础规划 + 执行分析")
    print("3. 退出")

    while True:
        choice = input("\n请输入选择 (1/2/3): ").strip()
        if choice == "1":
            app = build_enhanced_graph()
            mode = "增强模式"
            break
        elif choice == "2":
            app = build_traditional_graph()
            mode = "传统模式"
            break
        elif choice == "3":
            print("👋 再见！")
            return
        else:
            print("❌ 无效选择，请输入 1、2 或 3")

    logger.info(f"选择了 {mode}")

    # 从终端获取用户输入
    topic = input("\n请输入您想分析的产品、公司或领域：")
    logger.info(f"用户输入: {topic}")

    # 定义初始状态
    initial_state = {"initial_input": topic}

    print(f"\n🚀 开始执行竞品分析 Agent ({mode})")
    logger.info(f"开始执行竞品分析 Agent ({mode})")
    logger.info("=" * 80)

    try:
        # 流式执行图，并打印每一步的状态变化
        for s in app.stream(initial_state, {"recursion_limit": 150}):
            # s 是一个字典，key是刚刚执行完的节点名
            node_name = list(s.keys())[0]
            node_output = s[node_name]

            print(f"✅ 节点 '{node_name}' 执行完毕")
            logger.info(f"节点 '{node_name}' 执行完毕")
            logger.debug(f"节点输出: {node_output}")

        print("\n🎉 分析完成！")
        logger.info("=" * 80)
        logger.info("Agent 执行完成")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        logger.warning("用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
        logger.error(f"执行过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()