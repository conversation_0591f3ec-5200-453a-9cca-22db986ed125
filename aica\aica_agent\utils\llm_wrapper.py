# aica_agent/utils/llm_wrapper.py
"""
LLM包装器，添加超时控制和详细日志记录
"""

import time
import threading
import signal
from typing import Any, Dict, Optional
from langchain_core.runnables import Runnable
from .logger import get_logger


class TimeoutError(Exception):
    """超时异常"""
    pass


class LLMWrapper:
    """LLM包装器，提供超时控制和详细日志"""
    
    def __init__(self, llm: Runnable, timeout: int = 120, name: str = "LLM"):
        self.llm = llm
        self.timeout = timeout
        self.name = name
        self.logger = get_logger()
        self._result = None
        self._exception = None
        self._completed = False
    
    def invoke(self, inputs: Any, **kwargs) -> Any:
        """带超时控制的LLM调用"""
        self.logger.info(f"[LLM] [CALL_START] 开始调用{self.name}", "LLM")
        self.logger.info(f"[LLM] [TIMEOUT] 超时设置: {self.timeout}秒", "LLM")
        
        start_time = time.time()
        
        # 重置状态
        self._result = None
        self._exception = None
        self._completed = False
        
        # 启动心跳监控
        heartbeat_thread = threading.Thread(target=self._heartbeat_monitor, args=(start_time,))
        heartbeat_thread.daemon = True
        heartbeat_thread.start()
        
        # 在单独线程中执行LLM调用
        llm_thread = threading.Thread(target=self._llm_call, args=(inputs, kwargs))
        llm_thread.daemon = True
        llm_thread.start()
        
        # 等待完成或超时
        llm_thread.join(timeout=self.timeout)
        
        end_time = time.time()
        duration = end_time - start_time
        
        if llm_thread.is_alive():
            # 超时了
            self.logger.error(f"[LLM] [TIMEOUT_ERROR] {self.name}调用超时: {duration:.2f}秒", "LLM")
            raise TimeoutError(f"{self.name}调用超时 ({self.timeout}秒)")
        
        if self._exception:
            # 有异常
            self.logger.error(f"[LLM] [CALL_ERROR] {self.name}调用失败: {self._exception}", "LLM")
            raise self._exception
        
        if self._completed:
            # 正常完成
            self.logger.info(f"[LLM] [CALL_END] {self.name}调用完成, 耗时: {duration:.2f}秒", "LLM")
            return self._result
        else:
            # 未知状态
            self.logger.error(f"[LLM] [UNKNOWN_ERROR] {self.name}调用状态未知", "LLM")
            raise RuntimeError(f"{self.name}调用状态未知")
    
    def _llm_call(self, inputs: Any, kwargs: Dict[str, Any]):
        """在单独线程中执行LLM调用"""
        try:
            self.logger.debug(f"[LLM] [THREAD_START] LLM调用线程启动", "LLM")
            self._result = self.llm.invoke(inputs, **kwargs)
            self._completed = True
            self.logger.debug(f"[LLM] [THREAD_END] LLM调用线程完成", "LLM")
        except Exception as e:
            self._exception = e
            self.logger.debug(f"[LLM] [THREAD_ERROR] LLM调用线程异常: {e}", "LLM")
    
    def _heartbeat_monitor(self, start_time: float):
        """心跳监控"""
        try:
            while not self._completed and not self._exception:
                time.sleep(10)  # 每10秒检查一次
                
                if self._completed or self._exception:
                    break
                
                elapsed = time.time() - start_time
                self.logger.info(f"[LLM] [HEARTBEAT] {self.name}仍在执行, 已耗时: {elapsed:.1f}秒", "LLM")
                
                # 如果接近超时，发出警告
                if elapsed > self.timeout * 0.8:
                    remaining = self.timeout - elapsed
                    self.logger.warning(f"[LLM] [WARNING] {self.name}即将超时, 剩余时间: {remaining:.1f}秒", "LLM")
        except Exception as e:
            self.logger.error(f"[LLM] [HEARTBEAT_ERROR] 心跳监控异常: {e}", "LLM")


def create_llm_with_timeout(llm: Runnable, timeout: int = 120, name: str = "LLM") -> LLMWrapper:
    """创建带超时控制的LLM包装器"""
    return LLMWrapper(llm, timeout, name)


def wrap_chain_with_timeout(chain: Runnable, timeout: int = 120, name: str = "Chain") -> Runnable:
    """为chain添加超时控制"""
    from langchain_core.runnables import RunnableLambda
    
    def timeout_wrapper(inputs):
        wrapper = LLMWrapper(chain, timeout, name)
        return wrapper.invoke(inputs)
    
    return RunnableLambda(timeout_wrapper, name=f"{name}_with_timeout")


# 便捷函数
def create_planning_chain_with_timeout(chain, timeout=120):
    """创建带超时的规划chain"""
    return wrap_chain_with_timeout(chain, timeout, "planning_chain")


def create_competitor_identification_chain_with_timeout(chain, timeout=120):
    """创建带超时的竞品识别chain"""
    return wrap_chain_with_timeout(chain, timeout, "competitor_identification_chain")


def create_competitor_selection_chain_with_timeout(chain, timeout=120):
    """创建带超时的竞品选择chain"""
    return wrap_chain_with_timeout(chain, timeout, "competitor_selection_chain")


def create_keyword_generation_chain_with_timeout(chain, timeout=120):
    """创建带超时的关键词生成chain"""
    return wrap_chain_with_timeout(chain, timeout, "keyword_generation_chain")


def create_content_relevance_chain_with_timeout(chain, timeout=120):
    """创建带超时的内容相关性chain"""
    return wrap_chain_with_timeout(chain, timeout, "content_relevance_chain")


def create_content_analysis_chain_with_timeout(chain, timeout=120):
    """创建带超时的内容分析chain"""
    return wrap_chain_with_timeout(chain, timeout, "content_analysis_chain")


def create_subtask_execution_chain_with_timeout(chain, timeout=120):
    """创建带超时的子任务执行chain"""
    return wrap_chain_with_timeout(chain, timeout, "subtask_execution_chain")


def create_validation_chain_with_timeout(chain, timeout=120):
    """创建带超时的验证chain"""
    return wrap_chain_with_timeout(chain, timeout, "validation_chain")


def create_final_report_chain_with_timeout(chain, timeout=120):
    """创建带超时的最终报告chain"""
    return wrap_chain_with_timeout(chain, timeout, "final_report_chain")
