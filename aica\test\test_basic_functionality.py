#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试基本功能 - 不依赖LLM服务
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_prompt_import():
    """测试prompt导入"""
    print("测试prompt导入...")
    
    try:
        from aica_agent.prompts import (
            PLANNING_PROMPT,
            COMPETITOR_IDENTIFICATION_PROMPT
        )
        print("✅ Prompt导入成功")
        return True
    except Exception as e:
        print(f"❌ Prompt导入失败: {e}")
        return False


def test_prompt_formatting():
    """测试prompt格式化"""
    print("测试prompt格式化...")
    
    try:
        from aica_agent.prompts import PLANNING_PROMPT
        
        # 测试格式化
        formatted = PLANNING_PROMPT.format(initial_input="中医辅助诊疗系统")
        print(f"✅ Prompt格式化成功，长度: {len(formatted)}")
        return True
    except Exception as e:
        print(f"❌ Prompt格式化失败: {e}")
        return False


def test_chain_creation():
    """测试chain创建（不执行）"""
    print("测试chain创建...")
    
    try:
        from aica_agent.chains import (
            planning_chain,
            competitor_identification_chain
        )
        
        if planning_chain is not None:
            print("✅ planning_chain创建成功")
        else:
            print("❌ planning_chain为None")
            return False
            
        if competitor_identification_chain is not None:
            print("✅ competitor_identification_chain创建成功")
        else:
            print("❌ competitor_identification_chain为None")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Chain创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_logger_functionality():
    """测试日志功能"""
    print("测试日志功能...")
    
    try:
        from aica_agent.utils.chain_logger import log_function_call
        from aica_agent.utils.logger import get_logger
        
        logger = get_logger()
        logger.info("测试日志记录", "TEST")
        
        @log_function_call("test_function", "TEST")
        def test_func():
            return "测试结果"
        
        result = test_func()
        print(f"✅ 日志功能正常，结果: {result}")
        return True
    except Exception as e:
        print(f"❌ 日志功能失败: {e}")
        return False


def test_llm_connection():
    """测试LLM连接（简单测试）"""
    print("测试LLM连接...")
    
    try:
        from aica_agent.chains import llm
        
        # 简单测试LLM对象
        if llm is not None:
            print(f"✅ LLM对象创建成功，类型: {type(llm)}")
            if hasattr(llm, 'model_name'):
                print(f"   模型: {llm.model_name}")
            elif hasattr(llm, 'model'):
                print(f"   模型: {llm.model}")
            if hasattr(llm, 'openai_api_base'):
                print(f"   API Base: {llm.openai_api_base}")
            return True
        else:
            print("❌ LLM对象为None")
            return False
    except Exception as e:
        print(f"❌ LLM连接测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("基本功能测试（不依赖LLM服务）")
    print("=" * 60)
    
    tests = [
        ("Prompt导入", test_prompt_import),
        ("Prompt格式化", test_prompt_formatting),
        ("Chain创建", test_chain_creation),
        ("日志功能", test_logger_functionality),
        ("LLM连接", test_llm_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有基本功能测试通过！")
        print("\n建议:")
        print("1. 检查LLM服务是否正常运行")
        print("2. 检查网络连接到 http://192.168.0.92:81/v1")
        print("3. 如果LLM服务正常，可以尝试完整测试")
        return True
    else:
        print("❌ 部分基本功能测试失败，需要修复")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
