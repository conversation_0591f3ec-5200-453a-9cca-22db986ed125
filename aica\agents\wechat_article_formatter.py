# -*- coding: utf-8 -*-

"""
公众号文章排版智能体 (WeChat Article Formatter Agent)

本文件实现了一个基于LangGraph的AI智能体，旨在自动将JSON数据转换为一篇
排版精美的微信公众号文章（HTML格式）。

功能：
1.  接收一个JSON对象作为输入。
2.  通过多智能体协作（分析、策略、设计、开发、审核）的工作流进行处理。
3.  利用LLM生成吸引人的标题、副标题和文章内容。
4.  设计并应用适合移动端阅读的HTML和内联CSS样式。
5.  内置审核与迭代机制，确保输出质量。
6.  最终产出一个结构化的文章对象，包含标题和HTML正文。

如何作为模块使用:
    from wechat_article_formatter import run_wechat_formatter_agent, ArticleOutput
    import json

    # 确保设置了OPENAI_API_KEY环境变量
    # os.environ["OPENAI_API_KEY"] = "sk-..."

    json_data = {"product_name": "AI代码助手Pro", "features": ["智能补全", "bug检测", "自动生成文档"], "price": "¥199/月"}
    try:
        article = run_wechat_formatter_agent(json_data)
        print("--- 生成成功 ---")
        print(f"标题: {article.title}")
        print(f"副标题: {article.subtitle}")
        # print(f"HTML Body:\n{article.html_body}")
        # 将HTML保存到文件以便预览
        with open("article_preview.html", "w", encoding="utf-8") as f:
            f.write(article.html_body)
        print("\n文章已保存至 article_preview.html 文件，请在浏览器中打开查看效果。")
    except Exception as e:
        print(f"智能体运行出错: {e}")


如何作为命令行脚本使用:
    1.  创建一个名为 `input.json` 的文件，内容为您想转换的数据。
        例如: {"product_name": "AI代码助手Pro", "features": ["智能补全", "bug检测", "自动生成文档"], "price": "¥199/月"}
    2.  在终端中运行:
        python wechat_article_formatter.py input.json
    3.  结果将打印到控制台，并且HTML内容会保存到 `output.html` 文件。
"""

import os
import json
import argparse
import logging
import functools
from typing import List, Literal, Optional, Dict, Any

from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

from langgraph.graph import StateGraph, END

# --- 常量定义 ---
MAX_ITERATIONS = 3

# --- 1. 日志系统 (AICALogger & Decorator) ---

class AICALogger:
    """一个简单的日志记录器，用于智能体行为的可观察性。"""
    def __init__(self, name="AgentLogger", level=logging.INFO):
        self.logger = logging.getLogger(name)
        if not self.logger.handlers:
            self.logger.setLevel(level)
            ch = logging.StreamHandler()
            ch.setLevel(level)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            ch.setFormatter(formatter)
            self.logger.addHandler(ch)
            self.logger.propagate = False

    def info(self, msg, **kwargs):
        self.logger.info(self._format_msg(msg, **kwargs))

    def error(self, msg, **kwargs):
        self.logger.error(self._format_msg(msg, **kwargs))

    def _format_msg(self, msg, **kwargs):
        if kwargs:
            # 使用json.dumps美化输出，处理不可序列化的对象
            try:
                details = json.dumps(kwargs, indent=2, ensure_ascii=False, default=self._json_serializer)
                return f"{msg}\n{details}"
            except Exception as e:
                # 如果序列化失败，使用字符串表示
                details = str(kwargs)
                return f"{msg}\n{details}"
        return msg

    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理特殊对象类型"""
        # 处理Pydantic模型
        if hasattr(obj, 'model_dump'):
            return obj.model_dump()
        # 处理其他BaseModel子类
        elif hasattr(obj, 'dict'):
            return obj.dict()
        # 处理其他不可序列化的对象
        else:
            return str(obj)

# 初始化一个全局logger
logger = AICALogger("公众号文章排版智能体")

def log_node_io(func):
    """一个装饰器，用于自动记录节点的输入和输出状态。"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        node_name = func.__name__
        logger.info(f"--- Entering Node: {node_name} ---")

        # 记录输入状态
        if args:
            # 处理AgentState对象或字典
            state_input = args[0]
            if hasattr(state_input, 'model_dump'):
                # Pydantic模型
                state_dict = state_input.model_dump()
            elif hasattr(state_input, 'dict'):
                # 旧版Pydantic模型
                state_dict = state_input.dict()
            else:
                # 普通字典或其他对象
                state_dict = state_input

            logger.info(f"Input State:", state=state_dict)

        result = func(*args, **kwargs)

        # 记录输出
        logger.info(f"Output from {node_name}:", output=result)
        logger.info(f"--- Exiting Node: {node_name} ---\n")
        return result
    return wrapper

# --- 2. Pydantic 核心数据结构 ---

class AgentState(BaseModel):
    """定义了智能体在认知工作流中每一步的状态。"""
    original_json: Dict[str, Any] = Field(description="用户输入的原始JSON数据")
    analysis_notes: Optional[Dict[str, Any]] = Field(default=None, description="数据结构分析师对原始JSON的分析、主题和摘要")
    content_strategy: Optional[Dict[str, Any]] = Field(default=None, description="内容策略师提取的核心内容、标题选项和副标题")
    layout_plan: Optional[Dict[str, Any]] = Field(default=None, description="视觉设计师制定的排版和HTML结构计划")
    final_html: Optional[str] = Field(default=None, description="前端开发工程师生成的最终HTML文章")
    review_decision: Optional[Dict[str, Any]] = Field(default=None, description="总编辑的审稿决策和反馈意见")
    iteration_count: int = Field(default=0, description="当前迭代次数，用于防止无限循环")

# --- LLM 输出模型 ---
class AnalysisResult(BaseModel):
    """数据结构分析师的输出模型"""
    key_themes: List[str] = Field(description="从数据中提炼出的核心主题列表")
    content_summary: str = Field(description="对整个JSON数据内容的简短概括")
    suggested_outline: List[str] = Field(description="建议的文章内容大纲，分点列出")

class ContentStrategy(BaseModel):
    """内容策略师的输出模型"""
    title_options: List[str] = Field(description="3-5个吸引人但非标题党的文章标题备选项")
    subtitle: str = Field(description="用于补充和吸引读者的副标题")
    extracted_content: Dict[str, Any] = Field(description="根据大纲从原始数据中提取出的核心信息，以键值对形式组织")

class LayoutPlan(BaseModel):
    """视觉设计师的输出模型"""
    layout_description: str = Field(description="对排版设计的文字描述，说明各部分内容应使用的HTML标签和基本样式")
    basic_inline_css: Dict[str, str] = Field(description="建议用于关键HTML标签的内联CSS样式字典")

class GeneratedHTML(BaseModel):
    """前端开发工程师的输出模型"""
    html_output: str = Field(description="最终生成的、包含内联样式的完整HTML字符串")

class ReviewDecision(BaseModel):
    """总编辑的输出模型"""
    decision: Literal["accept", "refine"] = Field(description="审稿结论：'accept' (接受) 或 'refine' (需要修改)")
    feedback: str = Field(description="如果结论是 'refine'，必须提供清晰、具体的修改意见")

# --- 最终产出物模型 ---
class ArticleOutput(BaseModel):
    """智能体最终交付的产出物"""
    title: str
    subtitle: str
    html_body: str


# --- 3. 智能体节点定义 (Nodes & Personas) ---

# 初始化LLM和解析器
llm = ChatOpenAI(
    model="deepseek-r1-0528",
    api_key="",
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
    temperature=0.7
)

# 为每个结构化输出创建解析器
analysis_parser = JsonOutputParser(pydantic_object=AnalysisResult)
content_parser = JsonOutputParser(pydantic_object=ContentStrategy)
layout_parser = JsonOutputParser(pydantic_object=LayoutPlan)
html_parser = JsonOutputParser(pydantic_object=GeneratedHTML)
review_parser = JsonOutputParser(pydantic_object=ReviewDecision)

@log_node_io
def analyze_structure(state: AgentState) -> dict:
    """节点1: 数据结构分析师"""
    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are a meticulous Data Structure Analyst. Your task is to examine an arbitrary JSON object and infer its meaning and structure. Do not focus on formatting. Focus on understanding the data's essence.\n"
         "1. **Analyze Structure:** Examine the keys, value types (strings, numbers, lists), and nesting to understand the data's schema.\n"
         "2. **Infer Entity:** What real-world entity does this JSON represent? (e.g., a product, a report, a list of events).\n"
         "3. **Identify Key Themes:** What are the main topics or categories of information present?\n"
         "4. **Summarize Content:** Briefly summarize the core message or data points.\n"
         "5. **Propose Outline:** Suggest a logical flow for an article based on this data, like 'Introduction', 'Key Features', 'Conclusion'.\n"
         "Your output must be a JSON object that strictly conforms to the `AnalysisResult` model.\n{format_instructions}"),
        ("user", "Please analyze the following JSON data:\n```json\n{json_data}\n```")
    ]).partial(format_instructions=analysis_parser.get_format_instructions())

    chain = prompt | llm | analysis_parser
    result = chain.invoke({"json_data": json.dumps(state.original_json, ensure_ascii=False)})
    return {"analysis_notes": result}

@log_node_io
def extract_and_title(state: AgentState) -> dict:
    """节点2: 资深内容策略师"""
    feedback_section = ""
    if state.review_decision and 'feedback' in state.review_decision and state.review_decision['feedback']:
        feedback_section = f"**IMPORTANT Editor Feedback (must be addressed):**\n{state.review_decision['feedback']}\n"

    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are a Senior Content Strategist for a top-tier WeChat Official Account. You will receive a data analysis and, potentially, refinement feedback from your editor. Your goal is to devise a compelling content strategy.\n"
         "1. **Review Analysis & Feedback:** Carefully study the provided data analysis. {feedback_section}"
         "2. **Brainstorm Titles:** Generate 3-5 distinct, attractive, and informative titles. They must be relevant and avoid clickbait. A good title summarizes the article's core value.\n"
         "3. **Draft Subtitle:** Write a concise, one-sentence subtitle that expands on the title and draws the reader in.\n"
         "4. **Extract Core Content:** Systematically extract key information from the original JSON, organizing it according to the proposed outline.\n"
         "Your output must be a JSON object that strictly conforms to the `ContentStrategy` model.\n{format_instructions}"),
        ("user",
         "Original JSON Data:\n```json\n{original_json}\n```\n\n"
         "Data Analysis & Outline:\n```json\n{analysis_notes}\n```\n\n"
         "Please create the content strategy.")
    ]).partial(format_instructions=content_parser.get_format_instructions())

    chain = prompt | llm | content_parser
    result = chain.invoke({
        "original_json": json.dumps(state.original_json, ensure_ascii=False),
        "analysis_notes": json.dumps(state.analysis_notes, ensure_ascii=False),
        "feedback_section": feedback_section
    })
    return {"content_strategy": result}

@log_node_io
def design_layout(state: AgentState) -> dict:
    """节点3: 视觉设计师与排版专家"""
    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are a Visual Designer and Typography Expert specializing in WeChat article formatting. You will receive the content strategy. Your job is to design a clean, readable, and visually appealing layout.\n"
         "1. **Analyze Content Structure:** Review the extracted content, title, and subtitle.\n"
         "2. **Define HTML Structure:** Propose a high-level HTML structure. Specify tags for each content part (e.g., `<h1>` for title, `<h2>` for subtitle, `<h3>` for section headers, `<ul>/<li>` for lists, `<blockquote>` for highlights).\n"
         "3. **Suggest Inline CSS:** Recommend simple, effective inline CSS for readability. Use a professional color palette (e.g., text: `#333333`, headers: `#111111`, borders: `#dddddd`). Define basic margins and padding to create visual space.\n"
         "Your output must be a JSON object that strictly conforms to the `LayoutPlan` model.\n{format_instructions}"),
        ("user", "Content Strategy:\n```json\n{content_strategy}\n```\nPlease create a layout plan.")
    ]).partial(format_instructions=layout_parser.get_format_instructions())

    chain = prompt | llm | layout_parser
    result = chain.invoke({"content_strategy": json.dumps(state.content_strategy, ensure_ascii=False)})
    return {"layout_plan": result}

@log_node_io
def generate_html(state: AgentState) -> dict:
    """节点4: 前端开发工程师"""
    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are a meticulous Frontend Developer. You will receive the final content and a designer's layout plan. Your task is to write the complete, clean, and valid HTML code for the WeChat article.\n"
         "1. **Strictly Follow the Plan:** Combine the content from `content_strategy` with the layout structure and CSS from the `layout_plan`.\n"
         "2. **Implement as a Single HTML String:** Create a single, self-contained HTML string. Wrap everything in a main `<div>` with some padding (e.g., `style='padding: 16px;'`). Apply all styling via inline `style` attributes.\n"
         "3. **Ensure Readability and Quality:** Ensure proper tag nesting and spacing for a professional look. Use the provided title and subtitle in the content.\n"
         "Your output must be a JSON object that strictly conforms to the `GeneratedHTML` model.\n{format_instructions}"),
        ("user",
         "Content Strategy:\n```json\n{content_strategy}\n```\n\n"
         "Layout Plan:\n```json\n{layout_plan}\n```\n\n"
         "Please generate the final HTML.")
    ]).partial(format_instructions=html_parser.get_format_instructions())

    chain = prompt | llm | html_parser
    result = chain.invoke({
        "content_strategy": json.dumps(state.content_strategy, ensure_ascii=False),
        "layout_plan": json.dumps(state.layout_plan, ensure_ascii=False)
    })
    return {"final_html": result['html_output']}

@log_node_io
def quality_review(state: AgentState) -> dict:
    """节点5: 总编辑"""
    iteration_count = state.iteration_count + 1
    prompt = ChatPromptTemplate.from_messages([
        ("system",
         "You are the Editor-in-Chief, the final gatekeeper for quality. Review the generated article draft.\n"
         "1. **Assess Title & Subtitle:** Are they engaging, relevant, and non-clickbait? (from `content_strategy`)\n"
         "2. **Review Layout & Readability:** Is the HTML layout clean, professional, and easy to read? Is the visual hierarchy clear?\n"
         "3. **Check Content Fidelity:** Does the article accurately reflect the core information from the data analysis?\n"
         "4. **Make a Decision & Provide Feedback:**\n"
         "   - If it meets all standards, decide 'accept'. Feedback can be a simple 'Looks great!'.\n"
         "   - If there are any issues (e.g., weak title, cluttered layout), decide 'refine' and YOU MUST provide clear, actionable feedback on what to fix.\n"
         "Your output must be a JSON object that strictly conforms to the `ReviewDecision` model.\n{format_instructions}"),
        ("user",
         "Content Strategy:\n```json\n{content_strategy}\n```\n\n"
         "Generated HTML:\n```html\n{final_html}\n```\n\n"
         "Please provide your review.")
    ]).partial(format_instructions=review_parser.get_format_instructions())

    chain = prompt | llm | review_parser
    result = chain.invoke({
        "content_strategy": json.dumps(state.content_strategy, ensure_ascii=False),
        "final_html": state.final_html
    })
    return {"review_decision": result, "iteration_count": iteration_count}


# --- 4. 工作流路由逻辑 (Graph Edges) ---

def should_refine(state: AgentState) -> Literal["refine", "__end__"]:
    """判断是否需要迭代修改"""
    decision = state.review_decision['decision']
    iteration_count = state.iteration_count
    
    logger.info(f"Review decision: '{decision}'. Iteration: {iteration_count}/{MAX_ITERATIONS}.")
    
    if decision == "refine" and iteration_count < MAX_ITERATIONS:
        logger.info("Routing to 'refine' for another iteration.")
        return "refine"
    
    logger.info("Routing to '__end__'.")
    return "__end__"


# --- 5. 构建并编译 LangGraph 应用 ---

def build_graph():
    """构建并返回已编译的LangGraph应用"""
    workflow = StateGraph(AgentState)

    # 添加节点
    workflow.add_node("analyze_structure", analyze_structure)
    workflow.add_node("extract_and_title", extract_and_title)
    workflow.add_node("design_layout", design_layout)
    workflow.add_node("generate_html", generate_html)
    workflow.add_node("quality_review", quality_review)

    # 设置入口
    workflow.set_entry_point("analyze_structure")

    # 添加边
    workflow.add_edge("analyze_structure", "extract_and_title")
    workflow.add_edge("extract_and_title", "design_layout")
    workflow.add_edge("design_layout", "generate_html")
    workflow.add_edge("generate_html", "quality_review")

    # 添加条件边
    workflow.add_conditional_edges(
        "quality_review",
        should_refine,
        {
            "refine": "extract_and_title",
            "__end__": END
        }
    )

    # 编译图
    app = workflow.compile()
    return app

# --- 6. 主运行函数 (Public API) ---

def run_wechat_formatter_agent(json_input: dict) -> ArticleOutput:
    """
    运行公众号文章排版智能体。

    Args:
        json_input: 用户提供的原始JSON数据字典。

    Returns:
        一个 ArticleOutput 对象，包含最终的文章标题、副标题和HTML正文。
        
    Raises:
        ValueError: 如果OPENAI_API_KEY未设置。
        Exception: 如果在图执行过程中发生任何错误。
    """

    app = build_graph()
    initial_state = {"original_json": json_input}
    
    # 流式执行可以看到每一步的状态变化
    # for s in app.stream(initial_state):
    #     print(s)
    
    final_state = app.invoke(initial_state)

    if not final_state or not final_state.get('final_html'):
        raise Exception("智能体未能生成最终的HTML。请检查日志。")

    # 组装最终产出物
    content_strategy = final_state['content_strategy']
    article = ArticleOutput(
        title=content_strategy['title_options'][0] if content_strategy['title_options'] else "无标题",
        subtitle=content_strategy['subtitle'],
        html_body=final_state['final_html']
    )
    return article

# --- 7. 命令行入口 ---

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="公众号文章排版智能体命令行工具。")
    parser.add_argument("json_file", type=str, default=rf"T:\Agent\ai_agents\aica\test\aica.json", help="包含输入数据的JSON文件路径。")
    parser.add_argument("-o", "--output", type=str, default="output.html", help="保存生成的HTML的输出文件路径。")
    
    args = parser.parse_args()

    # try:
    #     from dotenv import load_dotenv
    #     load_dotenv()
    #     logger.info("环境变量已加载", "CONFIG")
    # except ImportError:
    #     logger.warning("python-dotenv 未安装，将从环境中直接读取API密钥", "CONFIG")
    # 检查API密钥


    # 读取并解析JSON文件
    try:
        with open(args.json_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {args.json_file}")
        exit(1)
    except json.JSONDecodeError:
        print(f"错误: 文件格式无效，请确保它是合法的JSON - {args.json_file}")
        exit(1)

    print(f"正在处理文件: {args.json_file}...")
    
    try:
        # 运行智能体
        final_article = run_wechat_formatter_agent(input_data)
        
        # 打印结果
        print("\n" + "="*50)
        print("🎉 文章生成成功!")
        print("="*50)
        print(f"\n📄 标题: {final_article.title}")
        print(f"📑 副标题: {final_article.subtitle}")
        
        # 保存HTML到文件
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(final_article.html_body)
        
        print(f"\n✅ HTML内容已保存至: {args.output}")
        print("💡 建议用浏览器打开该文件预览排版效果。")
        print("="*50)

    except Exception as e:
        print(f"\n❌ 在智能体运行过程中发生错误: {e}")
        logger.error(f"Agent execution failed with an exception.", exc_info=True)
        exit(1)