# config.py
import os
from dotenv import load_dotenv

load_dotenv()

# --- LLM and API Keys ---
OPENAI_API_KEY = "None"
LLM_MODEL = "GuanBao_BianCang_qwen2.5_7b_v0.0.1" # 使用更新、更强的模型

# --- Agent Control ---
MAX_COMPETITORS = 5
MAX_SUBTASK_RETRIES = 3 # 每个子任务的最大重试次数

# --- 竞品确认阶段参数 ---
INITIAL_SEARCH_RESULTS = 10  # 初始搜索结果数量
SELECTED_COMPETITORS = 3     # 选择进行深度研究的竞品数量

# --- 详细资料搜寻参数 ---
KEYWORDS_PER_COMPETITOR = 5  # 每个竞品生成的搜索关键词数量
MAX_PAGES_PER_KEYWORD = 3    # 每个关键词最多抓取的网页数量
CONTENT_ANALYSIS_THRESHOLD = 0.7  # 内容相关性阈值

# --- File Paths ---
REPORTS_DIR = "reports"
REPORT_FILENAME = "competitive_analysis_report.pdf"
CHINESE_FONT_PATH = rf"T:\AIGC\Learn\LLMs\langgraph\projects\aica\assets\TCCEB.TTF"