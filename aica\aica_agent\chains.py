# aica_agent/chains.py Agent "大脑"的核心，负责思考、规划、验证和总结。
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain_ollama import ChatOllama
from typing import List
# from .state import AnalysisScope, CompetitorProfile
from pydantic import BaseModel, Field
from .state import CompetitorAnalysis, SubTask, CompetitorInfo, SearchResult, DetailedContent
# 导入新的日志记录系统，替代callback
from .utils.chain_logger import (
    create_logging_chain,
    create_planning_chain_with_logging,
    create_competitor_identification_chain_with_logging,
    create_competitor_selection_chain_with_logging,
    create_keyword_generation_chain_with_logging,
    create_content_relevance_chain_with_logging,
    create_content_analysis_chain_with_logging,
    create_subtask_execution_chain_with_logging,
    create_validation_chain_with_logging,
    create_final_report_chain_with_logging,
    log_function_call
)
# 导入所有提示词模板
from .prompts import (
    ENHANCED_PLANNING_PROMPT,
    PLANNING_PROMPT,
    COMPETITOR_IDENTIFICATION_PROMPT,
    COMPETITOR_SELECTION_PROMPT,
    KEYWORD_GENERATION_PROMPT,
    CONTENT_RELEVANCE_PROMPT,
    CONTENT_ANALYSIS_PROMPT,
    SUBTASK_EXECUTION_PROMPT,
    VALIDATION_PROMPT,
    FINAL_REPORT_PROMPT
)
import os
from dotenv import load_dotenv
load_dotenv(os.path.join(os.getcwd(),".env"))

# 初始化LLM（单例模式，在整个应用中共享）
def get_ollama_llm(model_name="qwen2.5:32b"):
    base_url="http://192.168.0.92:8080"
    try:
        return ChatOllama(
            base_url=base_url, 
            model=model_name)
    except Exception as e:
        print(f"Error: {e}\ntry run ollama list to get model_name")
        return None
# 
# llm = get_ollama_llm()
llm = ChatOpenAI(
            model="qwen-max-latest",
            api_key=os.getenv("QWEN_API_KEY"),
            openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )

# --- 定义所有LLM链 ---

# --- 1. 意图推理 & 任务拆解 (规划) ---
class SubTaskDecomposition(BaseModel):
    task_name: str = Field(description="子任务的名称, 例如 '核心功能与产品定位'")
    acceptance_criteria: str = Field(description="用于验证该子任务是否完成的验收标准")

class Plan(BaseModel):
    competitors_to_research: List[str] = Field(description="基于用户输入识别出的具体竞品名称列表")
    sub_tasks: List[SubTaskDecomposition] = Field(description="为分析每个竞品而拆解出的通用子任务列表及其验收标准")

# 创建两个规划链（带日志记录支持）
@log_function_call("create_planning_chain", "PLANNING")
def create_planning_chain():
    base_chain = (
        PLANNING_PROMPT
        | llm.with_structured_output(Plan)
    )
    return create_planning_chain_with_logging(base_chain, enhanced=False)

@log_function_call("create_enhanced_planning_chain", "PLANNING")
def create_enhanced_planning_chain():
    base_chain = (
        ENHANCED_PLANNING_PROMPT
        | llm.with_structured_output(Plan)
    )
    return create_planning_chain_with_logging(base_chain, enhanced=True)

# 保持向后兼容
planning_chain = create_planning_chain()
enhanced_planning_chain = create_enhanced_planning_chain()

# --- 竞品确认阶段 ---

# 1. 竞品识别链
class CompetitorIdentificationResult(BaseModel):
    competitors: List[CompetitorInfo] = Field(description="识别出的竞品信息列表")

competitor_identification_chain = create_competitor_identification_chain_with_logging(
    COMPETITOR_IDENTIFICATION_PROMPT
    | llm.with_structured_output(CompetitorIdentificationResult)
)

# 2. 竞品选择链
class CompetitorSelectionResult(BaseModel):
    selected_competitors: List[CompetitorInfo] = Field(description="选择进行深度研究的竞品列表")
    selection_reasoning: str = Field(description="选择理由")

competitor_selection_chain = create_competitor_selection_chain_with_logging(
    COMPETITOR_SELECTION_PROMPT
    | llm.with_structured_output(CompetitorSelectionResult)
)

# --- 详细研究阶段 ---

# 3. 关键词生成链
class KeywordGenerationResult(BaseModel):
    keywords: List[str] = Field(description="生成的搜索关键词列表")
    reasoning: str = Field(description="关键词生成理由")

keyword_generation_chain = create_keyword_generation_chain_with_logging(
    KEYWORD_GENERATION_PROMPT
    | llm.with_structured_output(KeywordGenerationResult)
)

# 4. 内容相关性判断链
class ContentRelevanceResult(BaseModel):
    is_relevant: bool = Field(description="内容是否相关")
    relevance_score: float = Field(description="相关性评分 0-1")
    reasoning: str = Field(description="判断理由")
    key_points: List[str] = Field(description="关键信息点")

content_relevance_chain = create_content_relevance_chain_with_logging(
    CONTENT_RELEVANCE_PROMPT
    | llm.with_structured_output(ContentRelevanceResult)
)

# 5. 内容分析和总结链
class ContentAnalysisResult(BaseModel):
    summary: str = Field(description="内容总结")
    key_insights: List[str] = Field(description="关键洞察")
    product_features: List[str] = Field(description="产品特性")
    business_model: str = Field(description="商业模式分析")
    user_feedback: List[str] = Field(description="用户反馈要点")
    competitive_advantages: List[str] = Field(description="竞争优势")

content_analysis_chain = create_content_analysis_chain_with_logging(
    CONTENT_ANALYSIS_PROMPT
    | llm.with_structured_output(ContentAnalysisResult)
)

# --- 2. 子任务执行 ---
subtask_execution_chain = create_subtask_execution_chain_with_logging(
    SUBTASK_EXECUTION_PROMPT | llm
)

# --- 3. 反思与验证 ---
class ValidationResult(BaseModel):
    is_sufficient: bool = Field(description="判断结论是否满足验收标准")
    reasoning: str = Field(description="给出判断的理由。如果不足，请明确指出缺少了哪些信息。")

validation_chain = create_validation_chain_with_logging(
    VALIDATION_PROMPT | llm.with_structured_output(ValidationResult)
)

# --- 4. 最终报告生成 ---
final_report_chain = create_final_report_chain_with_logging(
    FINAL_REPORT_PROMPT | llm
)
