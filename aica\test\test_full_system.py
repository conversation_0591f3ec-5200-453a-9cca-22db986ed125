#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统测试 - 使用"中医辅助诊疗系统"作为输入
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_planning_chain_full():
    """测试完整的规划chain"""
    print("测试完整的规划chain...")
    
    try:
        from aica_agent.chains import planning_chain
        
        # 测试输入
        test_input = {
            "initial_input": "中医辅助诊疗系统"
        }
        
        print("开始执行规划chain...")
        print("输入:", test_input)
        
        start_time = time.time()
        result = planning_chain.invoke(test_input)
        end_time = time.time()
        
        print(f"执行完成！耗时: {end_time - start_time:.2f}秒")
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'dict'):
            result_dict = result.dict()
            competitors = result_dict.get('competitors_to_research', [])
            sub_tasks = result_dict.get('sub_tasks', [])
            
            print(f"\n识别的竞品数量: {len(competitors)}")
            print("竞品列表:")
            for i, competitor in enumerate(competitors, 1):
                print(f"  {i}. {competitor}")
            
            print(f"\n子任务数量: {len(sub_tasks)}")
            print("子任务列表:")
            for i, task in enumerate(sub_tasks, 1):
                task_name = task.get('task_name', 'Unknown') if isinstance(task, dict) else getattr(task, 'task_name', 'Unknown')
                print(f"  {i}. {task_name}")
        else:
            print(f"结果: {result}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        return True
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_competitor_identification_chain_full():
    """测试完整的竞品识别chain"""
    print("测试完整的竞品识别chain...")
    
    try:
        from aica_agent.chains import competitor_identification_chain
        
        # 模拟搜索结果
        search_results = """1. 标题: 大经中医智能辅助诊疗系统
   链接: http://dajingtcm.com/product
   摘要: 产品解决方案 中医智能辅助诊疗系统 名老中医经验智能化传承系统

2. 标题: 问止中医 中医大脑
   链接: https://techtcm.com/
   摘要: 中医大脑 中医人工智能 智能问诊系统

3. 标题: 云诊科技-中医舌诊AI
   链接: https://www.ai-tongue.com/
   摘要: 中医舌诊AI开放平台 中医AI辅助诊断系统

4. 标题: 梧明中医AI辅助诊断系统
   链接: https://www.muhuoai.com/
   摘要: 梧桐栖医道，明心见证源 医疗AI智能问诊平台

5. 标题: 脉景智能-中医人工智能
   链接: https://www.macrocura.com/
   摘要: MACROCURA 脉景智能 中医人工智能"""
        
        test_input = {
            "query": "中医辅助诊疗系统",
            "search_results": search_results
        }
        
        print("开始执行竞品识别chain...")
        print("输入查询:", test_input["query"])
        
        start_time = time.time()
        result = competitor_identification_chain.invoke(test_input)
        end_time = time.time()
        
        print(f"执行完成！耗时: {end_time - start_time:.2f}秒")
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'dict'):
            result_dict = result.dict()
            competitors = result_dict.get('competitors', [])
            
            print(f"\n识别的竞品数量: {len(competitors)}")
            print("竞品详情:")
            for i, competitor in enumerate(competitors, 1):
                if isinstance(competitor, dict):
                    name = competitor.get('name', 'Unknown')
                    company = competitor.get('company', 'Unknown')
                    score = competitor.get('relevance_score', 0)
                else:
                    name = getattr(competitor, 'name', 'Unknown')
                    company = getattr(competitor, 'company', 'Unknown')
                    score = getattr(competitor, 'relevance_score', 0)
                print(f"  {i}. {name} ({company}) - 相关性: {score}")
        else:
            print(f"结果: {result}")
        
        return True
        
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        return True
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_system_integration():
    """测试系统集成"""
    print("测试系统集成...")
    
    try:
        # 测试导入所有主要组件
        from aica_agent.chains import (
            planning_chain,
            enhanced_planning_chain,
            competitor_identification_chain,
            competitor_selection_chain
        )
        
        print("✅ 所有主要chain导入成功")
        
        # 测试日志系统
        from aica_agent.utils.logger import get_logger
        logger = get_logger()
        logger.info("系统集成测试", "INTEGRATION_TEST")
        
        print("✅ 日志系统正常")
        
        return True
        
    except Exception as e:
        print(f"系统集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("完整系统测试 - 中医辅助诊疗系统")
    print("=" * 60)
    
    tests = [
        ("系统集成", test_system_integration),
        ("规划Chain完整测试", test_planning_chain_full),
        ("竞品识别Chain完整测试", test_competitor_identification_chain_full),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有完整系统测试通过！")
        print("\n系统状态:")
        print("- ✅ 基本功能正常")
        print("- ✅ Chain执行正常")
        print("- ✅ 日志记录正常")
        print("- ✅ LLM连接正常")
        print("- ✅ 可以处理中医辅助诊疗系统查询")
        print("\n系统已准备就绪，可以正常使用！")
        return True
    else:
        print("❌ 部分完整系统测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
