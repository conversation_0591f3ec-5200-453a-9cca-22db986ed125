# aica_agent/prompts.py
# 所有LLM提示词模板的定义文件
from langchain_core.prompts import ChatPromptTemplate

# --- 1. 规划阶段提示词 ---

# 增强的规划提示词，包含搜索信息
ENHANCED_PLANNING_PROMPT = ChatPromptTemplate.from_template("""你是一个顶级的AI战略分析师。你的任务是理解用户的需求，并将其分解为一个详细、可执行的竞品分析计划。

用户的原始输入是: '{initial_input}'

**网络搜索信息:**
{search_context}

**你的思维过程 (Chain of Thought):**
1.  **识别核心实体**: 从用户输入中提取出核心的参照产品或领域。
2.  **理解用户意图**: 结合网络搜索信息，深入理解用户真正想要分析的内容。
3.  **识别具体竞品**: 基于搜索信息和核心实体，列出最相关、最重要的具体竞品名称（不要使用泛泛的描述）。
4.  **确定分析数量**: 根据领域特点确定合适的竞品分析数量（通常3-5个）。
5.  **设计分析框架**: 作为一个专业的分析师，设计一个通用的分析框架，至少包含4个关键分析维度。
6.  **设定验收标准**: 为每一个分析维度设定一个明确、可量化的验收标准。

**输出格式:**
请严格按照下面的JSON格式输出你的计划。每个子任务必须包含task_name和acceptance_criteria两个字段。

例如：
{{
  "competitors_to_research": ["具体竞品名称A", "具体竞品名称B", "具体竞品名称C"],
  "sub_tasks": [
    {{
      "task_name": "市场占有率分析",
      "acceptance_criteria": "提供每个竞品的市场份额数据和趋势分析"
    }},
    {{
      "task_name": "功能对比分析",
      "acceptance_criteria": "列出并比较各竞品的核心功能特性"
    }},
    {{
      "task_name": "商业模式分析",
      "acceptance_criteria": "分析各竞品的盈利模式和商业策略"
    }},
    {{
      "task_name": "用户评价分析",
      "acceptance_criteria": "收集并分析用户对各竞品的评价和反馈"
    }}
  ]
}}""")

# 原始规划提示词（作为备用）
PLANNING_PROMPT = ChatPromptTemplate.from_template("""你是一个顶级的AI战略分析师。你的任务是理解用户的需求，并将其分解为一个详细、可执行的竞品分析计划。

用户的原始输入是: '{initial_input}'

**你的思维过程 (Chain of Thought):**
1.  **识别核心实体**: 从用户输入中提取出核心的参照产品或领域。
2.  **识别数量**: 确定用户期望分析的竞品数量。
3.  **识别竞品**: 基于核心实体，列出最相关、最重要的竞品名称。
4.  **设计分析框架**: 作为一个专业的分析师，你应该知道一份标准的竞品分析报告包含哪些维度。请设计一个通用的分析框架，至少包含4个关键分析维度（子任务）。
5.  **设定验收标准**: 为每一个分析维度设定一个明确、可量化的验收标准。这是确保后续工作质量的关键。

**输出格式:**
请严格按照下面的JSON格式输出你的计划。每个子任务必须包含task_name和acceptance_criteria两个字段。

例如：
{{
  "competitors_to_research": ["竞品A", "竞品B"],
  "sub_tasks": [
    {{
      "task_name": "市场占有率分析",
      "acceptance_criteria": "提供每个竞品的市场份额数据和趋势分析"
    }},
    {{
      "task_name": "功能对比分析",
      "acceptance_criteria": "列出并比较各竞品的核心功能特性"
    }}
  ]
}}""")

# --- 2. 竞品确认阶段提示词 ---

# 竞品识别提示词
COMPETITOR_IDENTIFICATION_PROMPT = ChatPromptTemplate.from_template("""你是一个专业的市场分析师。基于搜索结果，识别和分析相关的竞品信息。

用户查询: '{query}'

搜索结果:
{search_results}

**你的任务:**
1. 从搜索结果中识别出与用户查询相关的产品/服务
2. 提取每个竞品的基本信息：名称、所属公司、主要功能
3. 评估每个竞品与用户查询的相关性（0-1分）
4. 只保留相关性评分 >= 0.6 的竞品

**输出格式:**
请严格按照JSON格式输出，包含competitors数组，每个竞品包含：
- name: 产品名称
- company: 所属公司
- main_functions: 主要功能列表
- description: 产品描述
- relevance_score: 相关性评分

例如：
{{
  "competitors": [
    {{
      "name": "微信小程序",
      "company": "腾讯",
      "main_functions": ["轻应用开发", "即用即走", "社交分享"],
      "description": "基于微信生态的轻量级应用平台",
      "relevance_score": 0.95
    }}
  ]
}}""")

# 竞品选择提示词
COMPETITOR_SELECTION_PROMPT = ChatPromptTemplate.from_template("""你是一个战略分析专家。从识别出的竞品中选择最值得深度研究的竞品。

用户查询: '{query}'

识别出的竞品:
{identified_competitors}

**选择标准:**
1. 相关性评分高
2. 市场影响力大
3. 功能特色明显
4. 代表性强

**要求:**
- 选择 {max_competitors} 个竞品进行深度研究
- 确保选择的竞品具有代表性和多样性
- 提供详细的选择理由

**输出格式:**
请严格按照以下JSON格式输出，字段名必须使用英文：
{{
  "selected_competitors": [
    {{
      "name": "产品名称",
      "company": "公司名称",
      "main_functions": ["功能1", "功能2"],
      "description": "产品描述",
      "relevance_score": 0.9
    }}
  ],
  "selection_reasoning": "选择这些竞品的详细理由"
}}

注意：字段名必须严格使用英文：name, company, main_functions, description, relevance_score""")

# --- 3. 详细研究阶段提示词 ---

# 关键词生成提示词
KEYWORD_GENERATION_PROMPT = ChatPromptTemplate.from_template("""你是一个信息检索专家。为指定竞品生成高质量的搜索关键词。

竞品信息:
- 名称: {competitor_name}
- 公司: {competitor_company}
- 主要功能: {main_functions}
- 描述: {description}

**任务:**
生成 {num_keywords} 个搜索关键词，用于深度了解这个竞品。

**关键词类型:**
1. 产品核心功能关键词
2. 商业模式关键词
3. 用户评价关键词
4. 技术特点关键词
5. 市场表现关键词

**要求:**
- 关键词要具体、准确
- 能够获取到有价值的信息
- 避免过于宽泛的词汇
- 考虑中文搜索习惯

**输出格式:**
{{
  "keywords": ["关键词1", "关键词2", ...],
  "reasoning": "生成这些关键词的理由和策略"
}}""")

# 内容相关性判断提示词
CONTENT_RELEVANCE_PROMPT = ChatPromptTemplate.from_template("""你是一个内容分析专家。判断网页内容是否与目标竞品相关，是否值得深度抓取。

目标竞品: {competitor_name}
搜索关键词: {keyword}

网页信息:
- 标题: {title}
- URL: {url}
- 摘要: {snippet}

**判断标准:**
1. 内容是否直接涉及目标竞品
2. 信息是否有价值（功能介绍、用户评价、市场分析等）
3. 内容是否足够详细和权威
4. 是否包含独特的见解或数据

**相关性评分:**
- 0.9-1.0: 高度相关，包含核心信息
- 0.7-0.8: 相关，包含有用信息
- 0.5-0.6: 部分相关，信息价值一般
- 0.0-0.4: 不相关或价值很低

**输出格式:**
{{
  "is_relevant": true/false,
  "relevance_score": 0.85,
  "reasoning": "判断理由",
  "key_points": ["关键信息点1", "关键信息点2"]
}}""")

# 内容分析和总结提示词
CONTENT_ANALYSIS_PROMPT = ChatPromptTemplate.from_template("""你是一个专业的产品分析师。分析收集到的网页内容，提取关于目标竞品的关键信息。

目标竞品: {competitor_name}

收集的内容:
{collected_content}

**分析维度:**
1. 产品功能和特性
2. 商业模式和盈利方式
3. 用户评价和反馈
4. 竞争优势和差异化
5. 市场表现和数据
6. 技术特点和创新

**要求:**
- 提取最重要和最有价值的信息
- 保持客观和准确
- 突出独特性和差异化
- 整理成结构化的分析结果

**输出格式:**
{{
  "summary": "整体总结",
  "key_insights": ["洞察1", "洞察2"],
  "product_features": ["功能1", "功能2"],
  "business_model": "商业模式分析",
  "user_feedback": ["用户反馈1", "用户反馈2"],
  "competitive_advantages": ["优势1", "优势2"]
}}""")

# --- 4. 子任务执行阶段提示词 ---

# 子任务执行提示词
SUBTASK_EXECUTION_PROMPT = ChatPromptTemplate.from_template("""你是一个高效的信息搜集员。你的目标是完成一个具体的调研任务。

**当前分析的竞品**: '{competitor}'
**需要完成的子任务**: '{sub_task}'
**你需要寻找的信息类型**: '{criteria}'

**你的行动指令:**
1.  **生成搜索词**: 基于以上信息，生成2-3个最可能找到答案的Google搜索查询词。
2.  **执行搜索与抓取**: (此部分由外部工具完成)
3.  **信息提取**: 你将收到一堆从网页上抓取下来的原始文本。请仔细阅读，并根据子任务和信息要求，用一段话总结出最关键的结论。

**原始文本资料:**
---
{scraped_content}
---

**你的结论 (一段总结性文字):**""")

# --- 5. 反思与验证阶段提示词 ---

# 验证提示词
VALIDATION_PROMPT = ChatPromptTemplate.from_template("""你是一个严格的质量控制员。你的职责是评估一份调研结论是否满足既定的质量标准。

**调研结论:**
'{conclusion}'

**验收标准:**
'{criteria}'

**你的任务:**
请判断上面的"调研结论"是否完全满足"验收标准"。
- 如果满足，请回答 `is_sufficient: true`。
- 如果不满足，请回答 `is_sufficient: false`，并在 `reasoning` 中清晰地说明为什么不满足，缺少了什么。""")

# --- 6. 最终报告生成阶段提示词 ---

# 最终报告生成提示词
FINAL_REPORT_PROMPT = ChatPromptTemplate.from_template("""你是一位首席战略官，正在撰写一份最终的竞品分析报告。
下面是已经通过质量验证的所有竞品的深度分析材料。

**分析材料:**
{analysis_summary}

请基于这些材料，撰写一份完整、逻辑清晰、富有洞察力的专业报告（Markdown格式）。
报告应包含执行摘要、对每个竞品的独立分析章节，以及最终的横向对比和战略建议。""")
