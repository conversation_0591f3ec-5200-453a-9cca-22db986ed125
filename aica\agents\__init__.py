# agents/__init__.py
"""
AICA Agents Package

This package contains all AI agents and shared utilities for the AICA system.

Structure:
- aica_agent/: Core competitive analysis agent
- tools/: Shared tools for web scraping, search, etc.
- utils/: Shared utilities for logging, LLM wrappers, etc.
- task_planner.py: Task planning agent
"""

# Import shared utilities
from .utils.logger import get_logger
from .utils.chain_logger import create_logging_chain
from .utils.llm_wrapper import create_llm_with_timeout

# Import shared tools
from .tools.web_tools import *

__all__ = [
    'get_logger',
    'create_logging_chain', 
    'create_llm_with_timeout'
]
