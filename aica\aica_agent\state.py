# aica_agent/state.py  状态是 Agent 的记忆

from typing import List, Dict, Optional, Any, Union

from dataclasses import dataclass
from pydantic import BaseModel, Field, field_validator

# 新增数据结构
class CompetitorInfo(BaseModel):
    """竞品基础信息"""
    name: str = Field(description="竞品名称")
    company: Optional[str] = Field(default="", description="所属公司")
    main_functions: List[str] = Field(description="主要功能列表")
    description: Optional[str] = Field(default="", description="产品描述")
    relevance_score: Optional[float] = Field(default=0.8, description="相关性评分 0-1")

    @field_validator('main_functions', mode='before')
    @classmethod
    def parse_main_functions(cls, v):
        """处理main_functions字段，支持多种格式到列表的转换"""
        import json

        if isinstance(v, str):
            # 如果是字符串，尝试解析JSON或按分隔符分割
            v = v.strip()
            if v.startswith('[') and v.endswith(']'):
                try:
                    # 尝试解析JSON数组
                    return json.loads(v)
                except json.JSONDecodeError:
                    pass
            # 按中文顿号或逗号分割
            return [func.strip() for func in v.replace('、', ',').split(',') if func.strip()]
        elif isinstance(v, list):
            # 如果已经是列表，检查每个元素
            result = []
            for item in v:
                if isinstance(item, str):
                    item = item.strip()
                    if item.startswith('[') and item.endswith(']'):
                        try:
                            # 如果列表中的元素是JSON字符串，解析它
                            parsed = json.loads(item)
                            if isinstance(parsed, list):
                                result.extend(parsed)
                            else:
                                result.append(str(parsed))
                        except json.JSONDecodeError:
                            result.append(item)
                    else:
                        result.append(item)
                else:
                    result.append(str(item))
            return result
        else:
            # 其他情况，尝试转换为字符串再处理
            return [str(v).strip()]

class SearchResult(BaseModel):
    """搜索结果"""
    title: str = Field(description="标题")
    url: str = Field(description="链接")
    snippet: str = Field(description="摘要")
    relevance_score: float = Field(description="相关性评分 0-1")

class DetailedContent(BaseModel):
    """详细内容"""
    url: str = Field(description="网页链接")
    title: str = Field(description="网页标题")
    content: str = Field(description="网页内容")
    keywords: List[str] = Field(description="相关关键词")
    summary: str = Field(description="内容摘要")

class CompetitorResearch(BaseModel):
    """竞品详细研究"""
    competitor_info: CompetitorInfo = Field(description="竞品基础信息")
    search_keywords: List[str] = Field(description="搜索关键词列表")
    collected_contents: List[DetailedContent] = Field(description="收集的详细内容")
    analysis_summary: str = Field(description="分析总结")
    is_complete: bool = Field(default=False)

class SubTask(BaseModel):
    """一个具体的、可执行的子任务"""
    task_name: str = Field(description="子任务的名称, 例如 '核心功能与产品定位'")
    acceptance_criteria: str = Field(description="用于验证该子任务是否完成的验收标准")
    result: Optional[str] = Field(default=None, description="该子任务执行后的结论")
    is_complete: bool = Field(default=False)

class CompetitorAnalysis(BaseModel):
    """对单个竞品的完整分析，包含多个子任务"""
    competitor_name: str
    sub_tasks: List[SubTask]

class AgentState(BaseModel):
    """定义整个图的状态"""
    # --- 初始输入 ---
    initial_input: str

    # --- 竞品确认阶段 ---
    initial_search_results: List[SearchResult] = Field(default_factory=list, description="初始搜索结果")
    identified_competitors: List[CompetitorInfo] = Field(default_factory=list, description="识别出的竞品信息")
    selected_competitors: List[CompetitorInfo] = Field(default_factory=list, description="选择进行深度研究的竞品")

    # --- 详细研究阶段 ---
    competitor_research: List[CompetitorResearch] = Field(default_factory=list, description="竞品详细研究结果")
    current_research_index: int = 0

    # --- 规划阶段（保留原有结构用于兼容） ---
    analysis_plan: List[CompetitorAnalysis] = Field(default_factory=list, description="完整的分析计划，包含所有竞品及其子任务")

    # --- 执行控制 ---
    # 使用 Any 来存储迭代器，因为 Pydantic 无法直接序列化它
    competitor_iterator: Any = Field(default=None, exclude=True, description="用于遍历竞品的迭代器")
    sub_task_iterator: Any = Field(default=None, exclude=True, description="用于遍历子任务的迭代器")

    current_competitor_name: Optional[str] = None
    current_sub_task_name: Optional[str] = None
    current_sub_task_criteria: Optional[str] = None
    current_retry_count: int = 0

    # --- 执行产物 ---
    current_sub_task_result: Optional[str] = None # 子任务执行的临时结果
    final_report_markdown: str = ""
    report_filepath: str = ""