from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser

llm = ChatOpenAI(
    model="GuanBao_BianCang_qwen2.5_7b_v0.0.1",  # 本地模型路径 vllm部署后model调用的名字
    api_key="Empty",  # 本地调用不需要 API key
    openai_api_base="http://192.168.0.92:81/v1"
)

from langchain_core.output_parsers import StrOutputParser
chain = (
  llm
  | StrOutputParser()
)
# response = chain.invoke("我需要基于langgraph开发一个ai智能体，请帮我列出详细的可执行的任务计划")
# response

response = chain.stream("我需要基于langgraph开发一个ai智能体，请帮我列出详细的可执行的任务计划")
for chunk in response:
    print(chunk, end="",flush=True)


res = chain.stream("阳明腑实证的具体表现有哪些？")
for chunk in res:
    print(chunk, end="",flush=True)