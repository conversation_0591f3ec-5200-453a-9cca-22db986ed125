# Cell 2: 导入与环境设置
import os
import pprint
from typing import Optional, TypedDict

from dotenv import load_dotenv

from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from langgraph.graph import END, StateGraph

# 加载 .env 文件中的环境变量
# 请确保您的项目中有一个 .env 文件，其中包含 OPENAI_API_KEY="sk-..."
load_dotenv()

print("环境设置完成。")

search_tool = DuckDuckGoSearchRun(name="web_search")

# 让我们测试一下工具是否正常工作
print("--- 测试 DuckDuckGo 搜索工具 ---")
test_query = "What is the current status of the Artemis program?"
test_result = search_tool.run(test_query)
print(f"查询: '{test_query}'")
print(f"结果: {test_result[:200]}...") # 只打印前200个字符

# Cell 4: 定义状态
class TaskPlannerState(TypedDict):
    """
    智能体的状态定义
    
    Attributes:
        original_goal: 用户输入的原始目标
        draft_plan: 计划草案
        critique: 对计划草案的批评和建议
        research_needed: 是否需要进行网络搜索
        research_query: 网络搜索的查询语句
        research_results: 从网络搜索得到的研究结果
        final_plan: 最终的、经过打磨的计划 (在图的早期版本中，我们用 draft_plan 来存储最终结果)
    """
    original_goal: str
    draft_plan: Optional[str]
    critique: Optional[str]
    research_needed: bool
    research_query: Optional[str]
    research_results: Optional[str]

print("智能体状态 (TaskPlannerState) 定义完成。")

model = ChatOpenAI(
            model="qwen-max-latest",
            api_key=os.getenv("QWEN_API_KEY"),
            openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
            temperature=0)

print(f"语言模型 ({model.model_name}) 初始化完成。")

# Cell 6: 定义规划师节点
def planner_node(state: TaskPlannerState):
    """规划师节点：根据目标和研究结果（如果有）生成初步计划。"""
    print("--- 👩‍💻 进入规划师节点 ---")
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system", 
         """你是一位世界级的项目管理专家和战略规划师。你的任务是根据用户的目标，通过严谨的思考过程，创建一个清晰、分阶段、可执行的任务计划。

            **请严格遵循以下思考流程:**

            1.  **[第一步：思考与分析]**
                *   **目标澄清:** 我是否完全理解了这个目标？是否存在模糊不清的地方？
                *   **信息评估:** 我现有的信息（包括研究资料）是否足够用来制定一个高质量的计划？
                *   **决策:** 如果信息不足，我必须进行研究。我需要搜索什么关键词？如果信息充足，我可以直接开始规划。

            2.  **[第二步：行动与输出]**
                *   **如果需要研究:** 请不要制定计划。直接在一行中输出你需要研究的查询语句，格式为：`RESEARCH_NEEDED: [你的搜索查询语句]`。
                *   **如果信息充足:**
                    *   **里程碑分解:** 将总体目标分解为3-5个逻辑清晰的主要阶段（Milestones/Phases）。
                    *   **任务细化:** 在每个阶段下，列出具体的、可操作的任务（Tasks）。
                    *   **格式化输出:** 使用 Markdown 格式化你的最终计划。"""),
        ("human", "用户目标: {original_goal}\n\n可用的研究资料:\n{research_results}")
    ])
    
    chain = prompt_template | model
    
    result = chain.invoke({
        "original_goal": state["original_goal"],
        "research_results": state.get("research_results")
    })
    
    response_text = result.content
    
    if "RESEARCH_NEEDED:" in response_text:
        query = response_text.split("RESEARCH_NEEDED:")[1].strip()
        print(f"规划师决策: 需要研究，查询: '{query}'")
        return {"research_needed": True, "research_query": query}
    else:
        print("规划师决策: 信息充足，已生成计划草案。")
        return {"research_needed": False, "draft_plan": response_text}

# --- 验证 planner_node ---
print("\n--- 测试规划师节点 ---")
test_state = {
    "original_goal": "我想学习如何使用 LangGraph 构建一个 AI 智能体。",
    "research_results": None
}
planner_output = planner_node(test_state)
pprint.pprint(planner_output)

# Cell 7: 定义研究员节点
def researcher_node(state: TaskPlannerState):
    """研究员节点：执行网络搜索。"""
    print("--- 🧑‍🔬 进入研究员节点 ---")
    query = state["research_query"]
    print(f"正在使用 DuckDuckGo 搜索: '{query}'")
    
    # 直接调用我们之前定义的 search_tool
    results = search_tool.run(query)
    
    print("研究完成。")
    return {"research_results": results}

# --- 验证 researcher_node ---
print("\n--- 测试研究员节点 ---")
test_state = {"research_query": "LangGraph tutorial for beginners"}
researcher_output = researcher_node(test_state)
print("研究结果（部分）:")
print(researcher_output['research_results'][:300] + "...")

# Cell 8: 定义批评家节点
def critique_node(state: TaskPlannerState):
    """批评家节点：审查计划草案并提供反馈。"""
    print("--- 🧐 进入批评家节点 ---")
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system", 
         """你是一位严谨的质量保证工程师，负责审查项目计划。你的任务是评估以下计划草案，找出其中的逻辑谬误、遗漏步骤或潜在风险。

            **你的输出:**
            *   如果计划质量很高，没有任何问题，请只回答 `NO_ISSUES`。
            *   否则，以列表形式清晰地列出你需要提出的所有批评和改进建议。"""),
        ("human", "计划草案:\n{draft_plan}")
    ])
    
    chain = prompt_template | model
    result = chain.invoke({"draft_plan": state["draft_plan"]})
    
    response_text = result.content.strip()
    
    if "NO_ISSUES" in response_text:
        print("批评家决策: 计划质量达标。")
        return {"critique": None}
    else:
        print("批评家决策: 计划存在缺陷，需要修订。")
        return {"critique": response_text}

# --- 验证 critique_node ---
print("\n--- 测试批评家节点 ---")
test_plan = """
### 学习 LangGraph 计划
- **阶段一**: 安装库
- **阶段二**: 运行代码
"""
test_state = {"draft_plan": test_plan}
critique_output = critique_node(test_state)
pprint.pprint(critique_output)

# Cell 9: 定义修订者节点
def reviser_node(state: TaskPlannerState):
    """修订者节点：根据批评意见修改计划。"""
    print("--- 📝 进入修订者节点 ---")
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system", 
         """你是一位经验丰富的项目经理，擅长根据反馈优化计划。你的任务是根据以下原始计划和批评意见，生成一个经过改进的新版本计划。
            请确保新计划解决了所有被指出的问题，并使用与原始计划相同的 Markdown 格式输出。"""),
        ("human", "原始计划:\n{draft_plan}\n\n批评与建议:\n{critique}")
    ])
    
    chain = prompt_template | model
    result = chain.invoke({
        "draft_plan": state["draft_plan"],
        "critique": state["critique"]
    })
    
    print("修订完成，生成新版计划草案。")
    # 修订后，清除旧的批评意见
    return {"draft_plan": result.content, "critique": None}

# --- 验证 reviser_node ---
print("\n--- 测试修订者节点 ---")
test_state = {
    "draft_plan": test_plan, # 使用上一个单元格的测试计划
    "critique": "- 计划过于简单，没有提到核心概念的学习，比如 State, Node, Edge。\n- 没有包含调试和部署的步骤。"
}
reviser_output = reviser_node(test_state)
print("修订后的计划草案:")
print(reviser_output['draft_plan'])

# Cell 10: 定义边的决策逻辑
def decide_to_research(state: TaskPlannerState):
    """决策：规划后是否需要研究？"""
    if state["research_needed"]:
        return "researcher"
    else:
        return "critique"

def decide_to_revise(state: TaskPlannerState):
    """决策：批判后是否需要修订？"""
    if state["critique"] is None:
        # 如果没有批评意见，说明计划完美，流程结束
        print("决策：计划通过审查，流程结束。")
        return END
    else:
        # 如果有批评意见，进入修订流程
        print("决策：计划需要修订。")
        return "reviser"

print("边的决策函数定义完成。")

# Cell 11: 构建和编译图
workflow = StateGraph(TaskPlannerState)

# 添加节点
workflow.add_node("planner", planner_node)
workflow.add_node("researcher", researcher_node)
workflow.add_node("critic", critique_node)
workflow.add_node("reviser", reviser_node)

# 设置图的入口点
workflow.set_entry_point("planner")

# 添加边
workflow.add_conditional_edges(
    "planner",
    decide_to_research,
    {"researcher": "researcher", "critique": "critic"}
)
workflow.add_edge("researcher", "planner") # 研究完后，返回规划师利用新知识

workflow.add_conditional_edges(
    "critic",
    decide_to_revise,
    {END: END, "reviser": "reviser"}
)
workflow.add_edge("reviser", "critic") # 修订完后，返回批评家进行新一轮评估

# 编译图，生成可执行的应用
app = workflow.compile()

print("计算图构建并编译完成！")

# 我们可以打印出图的结构
from IPython.display import Image, display
try:
    display(Image(app.get_graph().draw_mermaid_png()))
except Exception:
    print("无法生成图片，请确保安装了 'pygraphviz' 或 'pydot'。")
    pass

# Cell 12: 运行智能体
# 定义一个初始目标
# 这是一个比较专业的领域，很可能会触发研究流程
initial_goal = "我需要基于langgraph构建一个任务规划智能体，请结合你的思维链和提示词，帮我设计这个智能体"

# 智能体开始工作的初始状态
initial_state = {"original_goal": initial_goal}

print("🚀 任务规划智能体启动！")
print(f"🎯 目标: {initial_goal}\n")

# 使用 stream() 方法可以实时看到智能体每一步的思考过程
final_state = None
for event in app.stream(initial_state, stream_mode="values"):
    final_state = event
    print("---")
    pprint.pprint(event, depth=2) # depth=2 可以避免打印过长的研究结果

# 从最终状态中提取最终计划
final_plan = final_state.get("draft_plan")

print("\n\n✅ 任务规划完成！")
print("--- 最终计划 ---")
print(final_plan)