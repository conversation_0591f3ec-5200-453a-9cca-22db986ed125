{"cells": [{"cell_type": "code", "execution_count": 1, "id": "97956feb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境设置完成。\n"]}], "source": ["# Cell 2: 导入与环境设置\n", "import os\n", "import pprint\n", "from typing import Optional, TypedDict\n", "\n", "from dotenv import load_dotenv\n", "\n", "from langchain_community.tools import DuckDuckGoSearchRun\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "from langgraph.graph import END, StateGraph\n", "\n", "# 加载 .env 文件中的环境变量\n", "# 请确保您的项目中有一个 .env 文件，其中包含 OPENAI_API_KEY=\"sk-...\"\n", "load_dotenv()\n", "\n", "print(\"环境设置完成。\")"]}, {"cell_type": "code", "execution_count": 2, "id": "ef5f5a13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 测试 DuckDuckGo 搜索工具 ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\ProgramData\\Miniconda3\\envs\\llms\\lib\\site-packages\\langchain_community\\utilities\\duckduckgo_search.py:63: RuntimeWarning: This package (`duckduckgo_search`) has been renamed to `ddgs`! Use `pip install ddgs` instead.\n", "  with DDGS() as ddgs:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["查询: 'What is the current status of the Artemis program?'\n", "结果: No good DuckDuckGo Search Result was found...\n"]}], "source": ["search_tool = DuckDuckGoSearchRun(name=\"web_search\")\n", "\n", "# 让我们测试一下工具是否正常工作\n", "print(\"--- 测试 DuckDuckGo 搜索工具 ---\")\n", "test_query = \"What is the current status of the Artemis program?\"\n", "test_result = search_tool.run(test_query)\n", "print(f\"查询: '{test_query}'\")\n", "print(f\"结果: {test_result[:200]}...\") # 只打印前200个字符"]}, {"cell_type": "code", "execution_count": 3, "id": "26c5c7c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用 Pydantic 的结构化状态 (TaskPlannerState) 定义完成。\n"]}], "source": ["# Cell 4: 定义结构化的状态 (Pydantic版)\n", "from typing import List, Optional, TypedDict\n", "from pydantic import BaseModel, Field\n", "\n", "# Pydantic模型：定义单个任务的结构\n", "class Task(BaseModel):\n", "    task_id: int = Field(..., description=\"任务的唯一标识符，从1开始递增。\")\n", "    description: str = Field(..., description=\"对任务的清晰、简洁的描述。\")\n", "    dependencies: List[int] = Field(default_factory=list, description=\"该任务依赖的其他任务的task_id列表。\")\n", "    acceptance_criteria: List[str] = Field(..., description=\"一个字符串列表，其中每一项都是一个具体的、可验证的验收标准。\")\n", "\n", "# Pydantic模型：定义完整的计划\n", "class Plan(BaseModel):\n", "    tasks: List[Task]\n", "\n", "# 智能体的状态定义\n", "class TaskPlannerState(TypedDict):\n", "    original_goal: str\n", "    draft_plan: Optional[Plan]  # 状态现在存储的是一个Plan对象\n", "    critique: Optional[str]\n", "    research_needed: bool\n", "    research_query: Optional[str]\n", "    research_results: Optional[str]\n", "    revision_count: int\n", "\n", "print(\"使用 Pydantic 的结构化状态 (TaskPlannerState) 定义完成。\")"]}, {"cell_type": "code", "execution_count": null, "id": "71b4022f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["语言模型 (qwen-max-latest) 初始化完成。\n"]}], "source": ["model = ChatOpenAI(\n", "            model=\"qwen-max-latest\",\n", "            api_key=os.getenv(\"QWEN_API_KEY\"),\n", "            openai_api_base=\"https://dashscope.aliyuncs.com/compatible-mode/v1\",\n", "            temperature=0)\n", "\n", "print(f\"语言模型 ({model.model_name}) 初始化完成。\")"]}, {"cell_type": "code", "execution_count": null, "id": "244592b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["规划师节点 (planner_node) 已更新为JSON输出模式。\n"]}], "source": ["# Cell 6: 定义规划师节点 (JSON输出版)\n", "import json\n", "from langchain_core.exceptions import OutputParserException\n", "from langchain_core.output_parsers import JsonOutputParser\n", "\n", "def planner_node(state: TaskPlannerState):\n", "    \"\"\"规划师节点：生成结构化的JSON任务计划。\"\"\"\n", "    print(\"--- 👩‍💻 进入规划师节点 (JSON模式) ---\")\n", "    \n", "    # 我们为JSON输出定义一个解析器，并获取其格式化指令\n", "    parser = JsonOutputParser(pydantic_object=Plan)\n", "    \n", "    prompt_template = ChatPromptTemplate.from_messages([\n", "        (\"system\",\n", "         \"\"\"你是一位顶级的系统架构师和项目经理，专门为AI自动化工作流设计任务计划。\n", "            你的任务是根据用户的目标，创建一个结构化的、机器可读的任务列表。\n", "            你必须严格遵循以下JSON格式，并且只输出JSON，不包含任何额外的解释或Markdown标记。\n", "\n", "            {format_instructions}\n", "         \"\"\"),\n", "        (\"human\", \n", "         \"\"\"请为以下用户目标制定一个任务计划。\n", "            用户目标: {original_goal}\n", "\n", "            **关键要求**:\n", "            1.  **任务分解**: 将目标分解为一系列具体、独立的任务。\n", "            2.  **依赖关系**: 明确每个任务的前置依赖任务。根任务的依赖为空列表 `[]`。\n", "            3.  **验收标准 (Acceptance Criteria)**: 为每个任务提供一组清晰、具体、可验证的验收标准。这是最重要的部分。验收标准必须是二元的（即可以明确判断为'完成'或'未完成'），而不是模糊的。例如，使用“代码通过所有单元测试”而不是“代码质量好”。\n", "\n", "            可用的研究资料 (如果有的话):\n", "            {research_results}\n", "            \"\"\"\n", "        )\n", "    ])\n", "    \n", "    chain = prompt_template | model | parser\n", "    \n", "    try:\n", "        plan = chain.invoke({\n", "            \"original_goal\": state[\"original_goal\"],\n", "            \"research_results\": state.get(\"research_results\"),\n", "            \"format_instructions\": parser.get_format_instructions()\n", "        })\n", "        print(\"规划师决策: 已成功生成结构化计划草案。\")\n", "        return {\"draft_plan\": Plan.model_validate(plan), \"research_needed\": False}\n", "    except Exception as e:\n", "        print(f\"规划师决策: 无法直接生成计划，需要先进行研究。错误: {e}\")\n", "        # 如果初始生成失败（可能是目标太复杂），则强制进入研究流程\n", "        # 这里的提示词也可以引导LLM在不清楚时直接要求研究，但这是一个备用方案\n", "        research_prompt = ChatPromptTemplate.from_template(\"为实现目标 '{goal}'，生成一个合适的网络搜索查询语句。\")\n", "        research_chain = research_prompt | model\n", "        query = research_chain.invoke({\"goal\": state[\"original_goal\"]}).content.strip()\n", "        return {\"research_needed\": True, \"research_query\": query}\n", "\n", "print(\"规划师节点 (planner_node) 已更新为JSON输出模式。\")"]}, {"cell_type": "code", "execution_count": 6, "id": "f5baced1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- 测试研究员节点 ---\n", "--- 🧑‍🔬 进入研究员节点 ---\n", "正在使用 DuckDuckGo 搜索: 'LangGraph tutorial for beginners'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\ProgramData\\Miniconda3\\envs\\llms\\lib\\site-packages\\langchain_community\\utilities\\duckduckgo_search.py:63: RuntimeWarning: This package (`duckduckgo_search`) has been renamed to `ddgs`! Use `pip install ddgs` instead.\n", "  with DDGS() as ddgs:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["研究完成。\n", "研究结果（部分）:\n", "LangGraph核心技术概念 LangGraph和LangChain同宗同源，底层架构完全相同、接口完全相通。 从开发者角度来说，LangGraph也是使用LangChain底层API来接入各类大模型、LangGraph … img 右侧区域是组件区域，包含大模型回话的工具、组件调用的工具、以及具体的mcp组件。 左侧部分是面板区域，可以对节点进行编排以及处理。 实战案例：N8n + 高德MCP = 本地化AI应 … May 25, 2024 · LangGraph 在 2024 年 1 月推出的新库名为 LangGraph，正如名字所示，它基于图这一数学概念，作为大型语言模型驱动应用的框架...\n"]}], "source": ["# Cell 7: 定义研究员节点\n", "def researcher_node(state: TaskPlannerState):\n", "    \"\"\"研究员节点：执行网络搜索。\"\"\"\n", "    print(\"--- 🧑‍🔬 进入研究员节点 ---\")\n", "    query = state[\"research_query\"]\n", "    print(f\"正在使用 DuckDuckGo 搜索: '{query}'\")\n", "    \n", "    # 直接调用我们之前定义的 search_tool\n", "    results = search_tool.run(query)\n", "    \n", "    print(\"研究完成。\")\n", "    return {\"research_results\": results}\n", "\n", "# --- 验证 researcher_node ---\n", "print(\"\\n--- 测试研究员节点 ---\")\n", "test_state = {\"research_query\": \"LangGraph tutorial for beginners\"}\n", "researcher_output = researcher_node(test_state)\n", "print(\"研究结果（部分）:\")\n", "print(researcher_output['research_results'][:300] + \"...\")"]}, {"cell_type": "code", "execution_count": 7, "id": "379f125e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["批评家节点 (critique_node) 已更新为JSON审查模式。\n"]}], "source": ["# Cell 8: 定义批评家节点 (JSON审查版)\n", "def critique_node(state: TaskPlannerState):\n", "    \"\"\"批评家节点：审查结构化的JSON计划。\"\"\"\n", "    print(\"--- 🧐 进入批评家节点 (JSON审查模式) ---\")\n", "    \n", "    # 将 Pydantic 对象转换为格式化的 JSON 字符串以便 LLM 读取\n", "    plan_json_str = state[\"draft_plan\"].model_dump_json(indent=2)\n", "    \n", "    prompt_template = ChatPromptTemplate.from_messages([\n", "        (\"system\",\n", "         \"\"\"你是一位经验丰富的QA（质量保证）工程师，专门审查自动化任务计划。\n", "            你的任务是审查以下JSON格式的任务计划，并找出其中的问题。\"\"\"),\n", "        (\"human\",\n", "         \"\"\"请审查以下JSON计划：\n", "            ```json\n", "            {plan}\n", "            ```\n", "\n", "            **请重点检查以下方面**:\n", "            1.  **逻辑漏洞**: 任务分解是否合理？是否存在明显的步骤遗漏？\n", "            2.  **依赖关系**: `dependencies` 是否正确？是否存在循环依赖或错误的依赖关系？\n", "            3.  **验收标准质量**: 这是最重要的！`acceptance_criteria` 是否具体、可衡量、可验证？它们是否足够清晰，可以让一个独立的AI智能体判断任务是否完成？\n", "                - **坏标准 (Bad)**: \"代码已优化\", \"文档已编写\"\n", "                - **好标准 (Good)**: \"代码性能测试显示延迟低于100ms\", \"API文档已生成并部署到/docs路径\", \"所有代码都通过了 linter 检查且没有错误\"\n", "\n", "            **你的输出**:\n", "            - 如果计划质量很高，没有任何问题，请只回答 `NO_ISSUES`。\n", "            - 否则，以列表形式清晰地列出你需要提出的所有批评和改进建议。\"\"\")\n", "    ])\n", "    \n", "    chain = prompt_template | model\n", "    result = chain.invoke({\"plan\": plan_json_str})\n", "    \n", "    response_text = result.content.strip()\n", "    \n", "    if \"NO_ISSUES\" in response_text:\n", "        print(\"批评家决策: 计划质量达标。\")\n", "        return {\"critique\": None}\n", "    else:\n", "        print(\"批评家决策: 计划存在缺陷，需要修订。\")\n", "        return {\"critique\": response_text}\n", "\n", "print(\"批评家节点 (critique_node) 已更新为JSON审查模式。\")"]}, {"cell_type": "code", "execution_count": 8, "id": "fd1731f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["修订者节点 (reviser_node) 已更新为JSON修订模式。\n"]}], "source": ["# Cell 9: 定义修订者节点 (JSON修订版)\n", "def reviser_node(state: TaskPlannerState):\n", "    \"\"\"修订者节点：根据批评意见修改JSON计划。\"\"\"\n", "    print(\"--- 📝 进入修订者节点 (JSON修订模式) ---\")\n", "    \n", "    parser = JsonOutputParser(pydantic_object=Plan)\n", "    plan_json_str = state[\"draft_plan\"].model_dump_json(indent=2)\n", "    \n", "    prompt_template = ChatPromptTemplate.from_messages([\n", "        (\"system\",\n", "         \"\"\"你是一位高级项目经理，擅长根据反馈迭代和优化项目计划。\n", "            你的任务是根据批评意见，修改原始的JSON任务计划。\n", "            你必须严格遵循原始的JSON格式，并且只输出JSON。\n", "            \n", "            {format_instructions}\"\"\"),\n", "        (\"human\",\n", "         \"\"\"请根据以下批评意见，修订这份JSON计划。\n", "\n", "            **批评意见**:\n", "            {critique}\n", "\n", "            **原始JSON计划**:\n", "            ```json\n", "            {original_plan}\n", "            ```\n", "\n", "            请输出一份完整的、经过修订的JSON计划。\"\"\")\n", "    ])\n", "    \n", "    chain = prompt_template | model | parser\n", "    \n", "    try:\n", "        revised_plan_dict = chain.invoke({\n", "            \"critique\": state[\"critique\"],\n", "            \"original_plan\": plan_json_str,\n", "            \"format_instructions\": parser.get_format_instructions()\n", "        })\n", "        \n", "        # 验证修订后的输出\n", "        revised_plan = Plan.model_validate(revised_plan_dict)\n", "        \n", "        print(\"修订完成，生成新版结构化计划草案。\")\n", "        current_count = state.get(\"revision_count\", 0)\n", "        return {\n", "            \"draft_plan\": revised_plan,\n", "            \"critique\": None,\n", "            \"revision_count\": current_count + 1\n", "        }\n", "    except Exception as e:\n", "        print(f\"修订失败，返回原始计划。错误: {e}\")\n", "        # 如果修订失败，为避免循环卡死，可以返回一个错误信息或保持原样\n", "        # 这里我们选择保持原样，并保留批评意见，让迭代上限来终止循环\n", "        return {}\n", "\n", "print(\"修订者节点 (reviser_node) 已更新为JSON修订模式。\")"]}, {"cell_type": "code", "execution_count": 9, "id": "d13fa92d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["边的决策函数定义完成，已添加最大迭代次数限制。\n"]}], "source": ["# Cell 10: 定义边的决策逻辑 (修正版)\n", "\n", "# 设定一个全局的最大修订次数常量，方便调整\n", "MAX_REVISIONS = 3\n", "\n", "def decide_to_research(state: TaskPlannerState):\n", "    \"\"\"决策：规划后是否需要研究？\"\"\"\n", "    if state[\"research_needed\"]:\n", "        return \"researcher\"\n", "    else:\n", "        # 在进入批评流程前，确保 revision_count 已初始化\n", "        if state.get(\"revision_count\") is None:\n", "            state[\"revision_count\"] = 0\n", "        return \"critic\" # 注意：这里是您上次修正后的节点名\n", "\n", "def decide_to_revise(state: TaskPlannerState):\n", "    \"\"\"决策：批判后是否需要修订？\"\"\"\n", "    revision_count = state.get(\"revision_count\", 0)\n", "    critique = state.get(\"critique\")\n", "    \n", "    print(f\"当前修订次数: {revision_count}\")\n", "\n", "    # 检查是否达到最大次数\n", "    if revision_count >= MAX_REVISIONS:\n", "        print(f\"决策：已达到最大修订次数 ({MAX_REVISIONS})。流程强制结束。\")\n", "        # 即使有问题，也必须结束循环\n", "        return END\n", "    \n", "    # 如果没有达到最大次数，再判断计划质量\n", "    if critique is None:\n", "        print(\"决策：计划通过审查，流程结束。\")\n", "        return END\n", "    else:\n", "        print(\"决策：计划需要修订。\")\n", "        return \"reviser\"\n", "\n", "print(\"边的决策函数定义完成，已添加最大迭代次数限制。\")"]}, {"cell_type": "code", "execution_count": 11, "id": "35fab153", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["计算图构建并编译完成！\n"]}], "source": ["# Cell 11: 构建和编译图 (最终版)\n", "workflow = StateGraph(TaskPlannerState)\n", "\n", "# 添加节点\n", "workflow.add_node(\"planner\", planner_node)\n", "workflow.add_node(\"researcher\", researcher_node)\n", "workflow.add_node(\"critic\", critique_node) # 节点名为 critic\n", "workflow.add_node(\"reviser\", reviser_node)\n", "\n", "# 设置图的入口点\n", "workflow.set_entry_point(\"planner\")\n", "\n", "# 添加边\n", "workflow.add_conditional_edges(\n", "    \"planner\",\n", "    decide_to_research,\n", "    {\"researcher\": \"researcher\", \"critic\": \"critic\"} # 目标是 critic\n", ")\n", "workflow.add_edge(\"researcher\", \"planner\")\n", "\n", "workflow.add_conditional_edges(\n", "    \"critic\", # 从 critic 出发\n", "    decide_to_revise,\n", "    {END: <PERSON><PERSON>, \"reviser\": \"reviser\"}\n", ")\n", "workflow.add_edge(\"reviser\", \"critic\") # 修订完返回 critic\n", "\n", "# 编译图\n", "app = workflow.compile()\n", "\n", "print(\"计算图构建并编译完成！\")"]}, {"cell_type": "code", "execution_count": 12, "id": "2f916b66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 任务规划智能体启动！(JSON输出模式)\n", "🎯 目标: 为一款新的移动端社交应用制定一个从开发完成到成功上线并获取首批1000个用户的完整市场推广计划。\n", "\n", "---\n", "--- 👩‍💻 进入规划师节点 (JSON模式) ---\n", "规划师决策: 已成功生成结构化计划草案。\n", "---\n", "当前计划中的任务数: 8\n", "--- 🧐 进入批评家节点 (JSON审查模式) ---\n", "批评家决策: 计划存在缺陷，需要修订。\n", "当前修订次数: 0\n", "决策：计划需要修订。\n", "---\n", "当前计划中的任务数: 8\n", "收到批评意见...\n", "--- 📝 进入修订者节点 (JSON修订模式) ---\n", "修订完成，生成新版结构化计划草案。\n", "---\n", "当前计划中的任务数: 9\n", "--- 🧐 进入批评家节点 (JSON审查模式) ---\n", "批评家决策: 计划质量达标。\n", "当前修订次数: 1\n", "决策：计划通过审查，流程结束。\n", "---\n", "当前计划中的任务数: 9\n", "\n", "\n", "✅ 任务规划完成！\n", "--- 最终任务计划 (JSON) ---\n", "{\n", "  \"tasks\": [\n", "    {\n", "      \"task_id\": 1,\n", "      \"description\": \"完成移动端社交应用的最终版本，并确保其在所有目标设备上稳定运行。\",\n", "      \"dependencies\": [],\n", "      \"acceptance_criteria\": [\n", "        \"应用通过至少95%的功能测试用例。\",\n", "        \"应用在iOS和Android平台上均无崩溃记录。\",\n", "        \"P0和P1级别的bug已全部修复。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 2,\n", "      \"description\": \"制定详细的市场推广策略，包括目标受众、渠道选择和预算分配。\",\n", "      \"dependencies\": [\n", "        1\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"文档中列出了目标用户的平均年龄范围、性别比例及核心兴趣点。\",\n", "        \"至少确定三个主要推广渠道（如社交媒体、广告平台、KOL合作等）。\",\n", "        \"提供完整的预算使用计划，金额误差不超过10%。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 3,\n", "      \"description\": \"设计并制作所有必要的营销材料，包括宣传视频、图片素材和文案。\",\n", "      \"dependencies\": [\n", "        2\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"所有的营销材料由品牌团队完成审核并签字确认。\",\n", "        \"每个推广渠道都有专属的定制化素材，包括社交媒体、广告平台、邮件营销三个主要渠道。\",\n", "        \"所有素材均已上传至指定的云端存储位置。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 4,\n", "      \"description\": \"建立与关键意见领袖(KOL)和潜在合作伙伴的关系网络。\",\n", "      \"dependencies\": [\n", "        2\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"联系并确认至少10位相关领域的KOL进行合作。\",\n", "        \"与至少3家相关企业签订合作协议，且协议需包含最低推广预算承诺。\",\n", "        \"整理出一份完整的关系管理表格，包含联系方式及合作细节。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 5,\n", "      \"description\": \"启动预热活动以吸引早期用户注册或加入邮件列表。\",\n", "      \"dependencies\": [\n", "        3\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"预热活动页面上线且功能正常。\",\n", "        \"收集到不少于500个潜在用户的邮箱地址或联系方式。\",\n", "        \"总互动量达到10,000次（点赞、转发、评论）。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 6,\n", "      \"description\": \"对所有市场推广材料和策略进行最终审核，确保符合发布标准。\",\n", "      \"dependencies\": [\n", "        3,\n", "        4\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"所有市场推广材料经过法务和品牌团队双重审核并通过。\",\n", "        \"推广策略文档经过高层审批并签字确认。\",\n", "        \"所有审核流程在任务结束前完成。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 7,\n", "      \"description\": \"正式发布应用，并同步开展全面的推广活动。\",\n", "      \"dependencies\": [\n", "        5,\n", "        6\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"应用成功提交至App Store和Google Play商店并在提交后7天内通过审核。\",\n", "        \"当天全渠道广告投放按计划执行。\",\n", "        \"预计观众数为10,000人，实际观看人数需达到8,000人。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 8,\n", "      \"description\": \"监控推广效果并对策略进行实时优化调整。\",\n", "      \"dependencies\": [\n", "        7\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"每日更新报告需包含日新增下载量、日活跃用户数、周留存率等核心指标。\",\n", "        \"根据数据分析结果，至少实施一次策略调整方案。\",\n", "        \"用户获取成本低于预期值的15%以内，预期值为每用户10美元。\"\n", "      ]\n", "    },\n", "    {\n", "      \"task_id\": 9,\n", "      \"description\": \"达成首批1000名真实用户的里程碑。\",\n", "      \"dependencies\": [\n", "        8\n", "      ],\n", "      \"acceptance_criteria\": [\n", "        \"后台系统显示累计注册用户数量达到1000人。\",\n", "        \"其中至少有700人为活跃用户（过去7天内登录过应用）。\",\n", "        \"用户留存率不低于30%（行业基准线）。\"\n", "      ]\n", "    }\n", "  ]\n", "}\n"]}], "source": ["# Cell 12: 运行智能体 (JSON版)\n", "\n", "# 定义初始目标\n", "initial_goal = \"为一款新的移动端社交应用制定一个从开发完成到成功上线并获取首批1000个用户的完整市场推广计划。\"\n", "\n", "# 初始状态\n", "initial_state = {\"original_goal\": initial_goal, \"revision_count\": 0}\n", "\n", "print(\"🚀 任务规划智能体启动！(JSON输出模式)\")\n", "print(f\"🎯 目标: {initial_goal}\\n\")\n", "\n", "# 运行流程\n", "final_state = None\n", "for event in app.stream(initial_state, stream_mode=\"values\"):\n", "    final_state = event\n", "    print(\"---\")\n", "    # 为了避免打印过多内容，我们只打印关键信息\n", "    if \"draft_plan\" in event and event[\"draft_plan\"]:\n", "         print(\"当前计划中的任务数:\", len(event[\"draft_plan\"].tasks))\n", "    if \"critique\" in event and event[\"critique\"]:\n", "        print(\"收到批评意见...\")\n", "    if \"research_query\" in event and event[\"research_query\"]:\n", "        print(\"需要研究:\", event[\"research_query\"])\n", "\n", "\n", "# --- 输出最终结果 ---\n", "print(\"\\n\\n✅ 任务规划完成！\")\n", "\n", "# 从最终状态中提取计划和可能的遗留问题\n", "if final_state.get(\"draft_plan\"):\n", "    # 使用 pydantic 的 model_dump_json 方法来获得格式优美的JSON字符串\n", "    final_plan_json = final_state[\"draft_plan\"].model_dump_json(indent=2)\n", "    print(\"--- 最终任务计划 (JSON) ---\")\n", "    print(final_plan_json)\n", "else:\n", "    print(\"未能生成最终计划。\")\n", "\n", "\n", "final_critique = final_state.get(\"critique\")\n", "if final_critique:\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(\"⚠️ 注意：智能体已达到最大迭代次数，但计划仍存在以下待解决问题：\")\n", "    print(\"=\"*50)\n", "    print(\"\\n--- 最终反馈 ---\")\n", "    print(final_critique)"]}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}