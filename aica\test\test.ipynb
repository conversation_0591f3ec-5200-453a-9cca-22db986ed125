{"cells": [{"cell_type": "code", "execution_count": 3, "id": "97956feb", "metadata": {}, "outputs": [], "source": ["from langchain.chat_models import ChatOpenAI\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "llm = ChatOpenAI(\n", "    model=\"GuanBao_BianCang_qwen2.5_7b_v0.0.1\",  # 本地模型路径 vllm部署后model调用的名字\n", "    api_key=\"Empty\",  # 本地调用不需要 API key\n", "    openai_api_base=\"http://192.168.0.92:81/v1\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "ef5f5a13", "metadata": {}, "outputs": [{"data": {"text/plain": ["'我是观宝，由观健在产研团队训练的人工智能助手，专门为解答你的问题、提供信息和进行对话而设计。如果你有任何问题或需要帮助，都可以随时问我。'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "chain = (\n", "  llm\n", "  | StrOutputParser()\n", ")\n", "response = chain.invoke(\"你好，你是谁？\")\n", "response"]}, {"cell_type": "code", "execution_count": 11, "id": "26c5c7c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "患者症见大便秘结、腹满硬痛、潮热、谵语，此为阳明腑实之象。阳明经主肌肉、主津液，腑实则导致气血运行不畅，故见大便秘结；腑气不通则腹满硬痛；阳明热盛，火热内炽，则可见潮热、谵语等症状。此证以实热为主，兼有腑气阻滞，病位在胃肠道，病性属实。\n", "</think>\n", "### 证名:\n", "阳明腑实证"]}], "source": ["res = chain.stream(\"阳明腑实证的具体表现有哪些？\")\n", "for chunk in res:\n", "    print(chunk, end=\"\",flush=True)"]}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 5}