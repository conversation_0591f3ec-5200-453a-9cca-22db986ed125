# Cell 2: 导入与环境设置
import os
import pprint
from typing import Optional, TypedDict

from dotenv import load_dotenv

from langchain_community.tools import DuckDuckGoSearchRun
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from langgraph.graph import END, StateGraph

# 加载 .env 文件中的环境变量
# 请确保您的项目中有一个 .env 文件，其中包含 OPENAI_API_KEY="sk-..."
load_dotenv()

print("环境设置完成。")

search_tool = DuckDuckGoSearchRun(name="web_search")

# 让我们测试一下工具是否正常工作
print("--- 测试 DuckDuckGo 搜索工具 ---")
test_query = "What is the current status of the Artemis program?"
test_result = search_tool.run(test_query)
print(f"查询: '{test_query}'")
print(f"结果: {test_result[:200]}...") # 只打印前200个字符

# Cell 4: 定义结构化的状态 (Pydantic版)
from typing import List, Optional, TypedDict
from pydantic import BaseModel, Field

# Pydantic模型：定义单个任务的结构
class Task(BaseModel):
    task_id: int = Field(..., description="任务的唯一标识符，从1开始递增。")
    description: str = Field(..., description="对任务的清晰、简洁的描述。")
    dependencies: List[int] = Field(default_factory=list, description="该任务依赖的其他任务的task_id列表。")
    acceptance_criteria: List[str] = Field(..., description="一个字符串列表，其中每一项都是一个具体的、可验证的验收标准。")

# Pydantic模型：定义完整的计划
class Plan(BaseModel):
    tasks: List[Task]

# 智能体的状态定义
class TaskPlannerState(TypedDict):
    original_goal: str
    draft_plan: Optional[Plan]  # 状态现在存储的是一个Plan对象
    critique: Optional[str]
    research_needed: bool
    research_query: Optional[str]
    research_results: Optional[str]
    revision_count: int

print("使用 Pydantic 的结构化状态 (TaskPlannerState) 定义完成。")

model = ChatOpenAI(
            model="qwen-max-latest",
            api_key=os.getenv("QWEN_API_KEY"),
            openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",
            temperature=0)

print(f"语言模型 ({model.model_name}) 初始化完成。")

# Cell 6: 定义规划师节点 (JSON输出版)
import json
from langchain_core.exceptions import OutputParserException
from langchain_core.output_parsers import JsonOutputParser

def planner_node(state: TaskPlannerState):
    """规划师节点：生成结构化的JSON任务计划。"""
    print("--- 👩‍💻 进入规划师节点 (JSON模式) ---")
    
    # 我们为JSON输出定义一个解析器，并获取其格式化指令
    parser = JsonOutputParser(pydantic_object=Plan)
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位顶级的系统架构师和项目经理，专门为AI自动化工作流设计任务计划。
            你的任务是根据用户的目标，创建一个结构化的、机器可读的任务列表。
            你必须严格遵循以下JSON格式，并且只输出JSON，不包含任何额外的解释或Markdown标记。

            {format_instructions}
         """),
        ("human", 
         """请为以下用户目标制定一个任务计划。
            用户目标: {original_goal}

            **关键要求**:
            1.  **任务分解**: 将目标分解为一系列具体、独立的任务。
            2.  **依赖关系**: 明确每个任务的前置依赖任务。根任务的依赖为空列表 `[]`。
            3.  **验收标准 (Acceptance Criteria)**: 为每个任务提供一组清晰、具体、可验证的验收标准。这是最重要的部分。验收标准必须是二元的（即可以明确判断为'完成'或'未完成'），而不是模糊的。例如，使用“代码通过所有单元测试”而不是“代码质量好”。

            可用的研究资料 (如果有的话):
            {research_results}
            """
        )
    ])
    
    chain = prompt_template | model | parser
    
    try:
        plan = chain.invoke({
            "original_goal": state["original_goal"],
            "research_results": state.get("research_results"),
            "format_instructions": parser.get_format_instructions()
        })
        print("规划师决策: 已成功生成结构化计划草案。")
        return {"draft_plan": Plan.model_validate(plan), "research_needed": False}
    except Exception as e:
        print(f"规划师决策: 无法直接生成计划，需要先进行研究。错误: {e}")
        # 如果初始生成失败（可能是目标太复杂），则强制进入研究流程
        # 这里的提示词也可以引导LLM在不清楚时直接要求研究，但这是一个备用方案
        research_prompt = ChatPromptTemplate.from_template("为实现目标 '{goal}'，生成一个合适的网络搜索查询语句。")
        research_chain = research_prompt | model
        query = research_chain.invoke({"goal": state["original_goal"]}).content.strip()
        return {"research_needed": True, "research_query": query}

print("规划师节点 (planner_node) 已更新为JSON输出模式。")

# Cell 7: 定义研究员节点
def researcher_node(state: TaskPlannerState):
    """研究员节点：执行网络搜索。"""
    print("--- 🧑‍🔬 进入研究员节点 ---")
    query = state["research_query"]
    print(f"正在使用 DuckDuckGo 搜索: '{query}'")
    
    # 直接调用我们之前定义的 search_tool
    results = search_tool.run(query)
    
    print("研究完成。")
    return {"research_results": results}

# --- 验证 researcher_node ---
print("\n--- 测试研究员节点 ---")
test_state = {"research_query": "LangGraph tutorial for beginners"}
researcher_output = researcher_node(test_state)
print("研究结果（部分）:")
print(researcher_output['research_results'][:300] + "...")

# Cell 8: 定义批评家节点 (JSON审查版)
def critique_node(state: TaskPlannerState):
    """批评家节点：审查结构化的JSON计划。"""
    print("--- 🧐 进入批评家节点 (JSON审查模式) ---")
    
    # 将 Pydantic 对象转换为格式化的 JSON 字符串以便 LLM 读取
    plan_json_str = state["draft_plan"].model_dump_json(indent=2)
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位经验丰富的QA（质量保证）工程师，专门审查自动化任务计划。
            你的任务是审查以下JSON格式的任务计划，并找出其中的问题。"""),
        ("human",
         """请审查以下JSON计划：
            ```json
            {plan}
            ```

            **请重点检查以下方面**:
            1.  **逻辑漏洞**: 任务分解是否合理？是否存在明显的步骤遗漏？
            2.  **依赖关系**: `dependencies` 是否正确？是否存在循环依赖或错误的依赖关系？
            3.  **验收标准质量**: 这是最重要的！`acceptance_criteria` 是否具体、可衡量、可验证？它们是否足够清晰，可以让一个独立的AI智能体判断任务是否完成？
                - **坏标准 (Bad)**: "代码已优化", "文档已编写"
                - **好标准 (Good)**: "代码性能测试显示延迟低于100ms", "API文档已生成并部署到/docs路径", "所有代码都通过了 linter 检查且没有错误"

            **你的输出**:
            - 如果计划质量很高，没有任何问题，请只回答 `NO_ISSUES`。
            - 否则，以列表形式清晰地列出你需要提出的所有批评和改进建议。""")
    ])
    
    chain = prompt_template | model
    result = chain.invoke({"plan": plan_json_str})
    
    response_text = result.content.strip()
    
    if "NO_ISSUES" in response_text:
        print("批评家决策: 计划质量达标。")
        return {"critique": None}
    else:
        print("批评家决策: 计划存在缺陷，需要修订。")
        return {"critique": response_text}

print("批评家节点 (critique_node) 已更新为JSON审查模式。")

# Cell 9: 定义修订者节点 (JSON修订版)
def reviser_node(state: TaskPlannerState):
    """修订者节点：根据批评意见修改JSON计划。"""
    print("--- 📝 进入修订者节点 (JSON修订模式) ---")
    
    parser = JsonOutputParser(pydantic_object=Plan)
    plan_json_str = state["draft_plan"].model_dump_json(indent=2)
    
    prompt_template = ChatPromptTemplate.from_messages([
        ("system",
         """你是一位高级项目经理，擅长根据反馈迭代和优化项目计划。
            你的任务是根据批评意见，修改原始的JSON任务计划。
            你必须严格遵循原始的JSON格式，并且只输出JSON。
            
            {format_instructions}"""),
        ("human",
         """请根据以下批评意见，修订这份JSON计划。

            **批评意见**:
            {critique}

            **原始JSON计划**:
            ```json
            {original_plan}
            ```

            请输出一份完整的、经过修订的JSON计划。""")
    ])
    
    chain = prompt_template | model | parser
    
    try:
        revised_plan_dict = chain.invoke({
            "critique": state["critique"],
            "original_plan": plan_json_str,
            "format_instructions": parser.get_format_instructions()
        })
        
        # 验证修订后的输出
        revised_plan = Plan.model_validate(revised_plan_dict)
        
        print("修订完成，生成新版结构化计划草案。")
        current_count = state.get("revision_count", 0)
        return {
            "draft_plan": revised_plan,
            "critique": None,
            "revision_count": current_count + 1
        }
    except Exception as e:
        print(f"修订失败，返回原始计划。错误: {e}")
        # 如果修订失败，为避免循环卡死，可以返回一个错误信息或保持原样
        # 这里我们选择保持原样，并保留批评意见，让迭代上限来终止循环
        return {}

print("修订者节点 (reviser_node) 已更新为JSON修订模式。")

# Cell 10: 定义边的决策逻辑 (修正版)

# 设定一个全局的最大修订次数常量，方便调整
MAX_REVISIONS = 3

def decide_to_research(state: TaskPlannerState):
    """决策：规划后是否需要研究？"""
    if state["research_needed"]:
        return "researcher"
    else:
        # 在进入批评流程前，确保 revision_count 已初始化
        if state.get("revision_count") is None:
            state["revision_count"] = 0
        return "critic" # 注意：这里是您上次修正后的节点名

def decide_to_revise(state: TaskPlannerState):
    """决策：批判后是否需要修订？"""
    revision_count = state.get("revision_count", 0)
    critique = state.get("critique")
    
    print(f"当前修订次数: {revision_count}")

    # 检查是否达到最大次数
    if revision_count >= MAX_REVISIONS:
        print(f"决策：已达到最大修订次数 ({MAX_REVISIONS})。流程强制结束。")
        # 即使有问题，也必须结束循环
        return END
    
    # 如果没有达到最大次数，再判断计划质量
    if critique is None:
        print("决策：计划通过审查，流程结束。")
        return END
    else:
        print("决策：计划需要修订。")
        return "reviser"

print("边的决策函数定义完成，已添加最大迭代次数限制。")

# Cell 11: 构建和编译图 (最终版)
workflow = StateGraph(TaskPlannerState)

# 添加节点
workflow.add_node("planner", planner_node)
workflow.add_node("researcher", researcher_node)
workflow.add_node("critic", critique_node) # 节点名为 critic
workflow.add_node("reviser", reviser_node)

# 设置图的入口点
workflow.set_entry_point("planner")

# 添加边
workflow.add_conditional_edges(
    "planner",
    decide_to_research,
    {"researcher": "researcher", "critic": "critic"} # 目标是 critic
)
workflow.add_edge("researcher", "planner")

workflow.add_conditional_edges(
    "critic", # 从 critic 出发
    decide_to_revise,
    {END: END, "reviser": "reviser"}
)
workflow.add_edge("reviser", "critic") # 修订完返回 critic

# 编译图
app = workflow.compile()

print("计算图构建并编译完成！")

# Cell 12: 运行智能体 (JSON版)

# 定义初始目标
initial_goal = "为一款新的移动端社交应用制定一个从开发完成到成功上线并获取首批1000个用户的完整市场推广计划。"

# 初始状态
initial_state = {"original_goal": initial_goal, "revision_count": 0}

print("🚀 任务规划智能体启动！(JSON输出模式)")
print(f"🎯 目标: {initial_goal}\n")

# 运行流程
final_state = None
for event in app.stream(initial_state, stream_mode="values"):
    final_state = event
    print("---")
    # 为了避免打印过多内容，我们只打印关键信息
    if "draft_plan" in event and event["draft_plan"]:
         print("当前计划中的任务数:", len(event["draft_plan"].tasks))
    if "critique" in event and event["critique"]:
        print("收到批评意见...")
    if "research_query" in event and event["research_query"]:
        print("需要研究:", event["research_query"])


# --- 输出最终结果 ---
print("\n\n✅ 任务规划完成！")

# 从最终状态中提取计划和可能的遗留问题
if final_state.get("draft_plan"):
    # 使用 pydantic 的 model_dump_json 方法来获得格式优美的JSON字符串
    final_plan_json = final_state["draft_plan"].model_dump_json(indent=2)
    print("--- 最终任务计划 (JSON) ---")
    print(final_plan_json)
else:
    print("未能生成最终计划。")


final_critique = final_state.get("critique")
if final_critique:
    print("\n" + "="*50)
    print("⚠️ 注意：智能体已达到最大迭代次数，但计划仍存在以下待解决问题：")
    print("="*50)
    print("\n--- 最终反馈 ---")
    print(final_critique)