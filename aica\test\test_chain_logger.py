#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的chain日志记录系统
验证移除callback后的日志功能是否正常
"""

import sys
import os
from unittest.mock import Mock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.utils.chain_logger import (
        ChainLogger,
        create_logging_chain,
        log_function_call
    )
    from aica_agent.chains import (
        planning_chain,
        enhanced_planning_chain,
        competitor_identification_chain
    )
    from langchain_core.runnables import RunnableLambda
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def test_chain_logger_basic():
    """测试ChainLogger基本功能"""
    print("测试ChainLogger基本功能...")
    
    logger = ChainLogger("test_chain", "TEST")
    
    # 测试输入日志
    test_input = {"query": "测试查询", "data": "测试数据"}
    result_input = logger.log_input(test_input)
    assert result_input == test_input, "输入应该被透传"
    print("   ✅ 输入日志记录正常")
    
    # 测试输出日志
    test_output = {"result": "测试结果", "status": "success"}
    result_output = logger.log_output(test_output)
    assert result_output == test_output, "输出应该被透传"
    print("   ✅ 输出日志记录正常")
    
    # 测试非字典输入
    string_input = "这是一个字符串输入"
    result_string = logger.log_input(string_input)
    assert result_string == string_input, "字符串输入应该被透传"
    print("   ✅ 非字典输入处理正常")
    
    return True


def test_logging_chain_creation():
    """测试带日志的chain创建"""
    print("\n测试带日志的chain创建...")
    
    # 创建一个简单的测试chain
    def simple_function(x):
        return {"processed": f"处理了: {x}"}
    
    simple_chain = RunnableLambda(simple_function)
    
    # 创建带日志的chain
    logged_chain = create_logging_chain(simple_chain, "test_simple_chain", "TEST")
    
    # 测试执行
    test_input = {"input": "测试数据"}
    try:
        result = logged_chain.invoke(test_input)
        print(f"   ✅ 带日志chain执行成功，结果: {result}")
        return True
    except Exception as e:
        print(f"   ❌ 带日志chain执行失败: {e}")
        return False


def test_function_decorator():
    """测试函数装饰器"""
    print("\n测试函数装饰器...")
    
    @log_function_call("test_decorated_function", "TEST")
    def test_function(x, y):
        return x + y
    
    try:
        result = test_function(1, 2)
        assert result == 3, "函数结果应该正确"
        print("   ✅ 函数装饰器工作正常")
        return True
    except Exception as e:
        print(f"   ❌ 函数装饰器失败: {e}")
        return False


def test_chain_integration():
    """测试与实际chain的集成"""
    print("\n测试与实际chain的集成...")
    
    try:
        # 测试planning_chain是否正常创建
        if planning_chain is not None:
            print("   ✅ planning_chain创建成功")
        else:
            print("   ❌ planning_chain创建失败")
            return False
        
        # 测试enhanced_planning_chain是否正常创建
        if enhanced_planning_chain is not None:
            print("   ✅ enhanced_planning_chain创建成功")
        else:
            print("   ❌ enhanced_planning_chain创建失败")
            return False
        
        # 测试competitor_identification_chain是否正常创建
        if competitor_identification_chain is not None:
            print("   ✅ competitor_identification_chain创建成功")
        else:
            print("   ❌ competitor_identification_chain创建失败")
            return False
        
        return True
    except Exception as e:
        print(f"   ❌ Chain集成测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    
    logger = ChainLogger("error_test_chain", "TEST")
    
    # 测试KeyboardInterrupt处理
    try:
        logger.log_error(KeyboardInterrupt("用户中断"))
        print("   ❌ KeyboardInterrupt应该被重新抛出")
        return False
    except KeyboardInterrupt:
        print("   ✅ KeyboardInterrupt正确重新抛出")
    except Exception as e:
        print(f"   ❌ 意外错误: {e}")
        return False
    
    # 测试普通异常处理
    try:
        logger.log_error(ValueError("测试错误"))
        print("   ✅ 普通异常处理正常")
        return True
    except Exception as e:
        print(f"   ❌ 普通异常处理失败: {e}")
        return False


def test_data_sanitization():
    """测试数据清理功能"""
    print("\n测试数据清理功能...")
    
    logger = ChainLogger("sanitize_test_chain", "TEST")
    
    # 测试长字符串截断
    long_string = "a" * 500
    test_data = {"long_field": long_string, "normal_field": "normal"}
    
    sanitized = logger._sanitize_data(test_data)
    
    if len(sanitized["long_field"]) < len(long_string):
        print("   ✅ 长字符串正确截断")
    else:
        print("   ❌ 长字符串未被截断")
        return False
    
    if sanitized["normal_field"] == "normal":
        print("   ✅ 正常字段保持不变")
    else:
        print("   ❌ 正常字段被意外修改")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 60)
    print("Chain日志记录系统测试")
    print("=" * 60)
    
    tests = [
        ("ChainLogger基本功能", test_chain_logger_basic),
        ("带日志Chain创建", test_logging_chain_creation),
        ("函数装饰器", test_function_decorator),
        ("Chain集成", test_chain_integration),
        ("错误处理", test_error_handling),
        ("数据清理", test_data_sanitization),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！新的日志记录系统工作正常！")
        print("\n新系统特点:")
        print("- ✅ 移除了callback依赖")
        print("- ✅ 使用RunnableLambda实现日志记录")
        print("- ✅ 显示chain名称和执行阶段")
        print("- ✅ 记录详细的输入输出信息")
        print("- ✅ 支持KeyboardInterrupt正确处理")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
