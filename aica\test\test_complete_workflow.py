#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的工作流测试 - 使用"中医辅助诊疗系统"作为输入
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from aica_agent.chains import planning_chain
    from aica_agent.utils.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在llms环境中运行此测试")
    sys.exit(1)


def test_planning_phase():
    """测试规划阶段"""
    print("=" * 60)
    print("测试规划阶段 - Planning Chain")
    print("=" * 60)
    
    logger = get_logger()
    
    # 测试输入
    test_input = {
        "initial_input": "中医辅助诊疗系统"
    }
    
    print(f"输入: {test_input['initial_input']}")
    print("\n开始执行planning_chain...")
    print("观察日志输出:")
    print("-" * 40)
    
    try:
        # 执行planning chain
        result = planning_chain.invoke(test_input)
        
        print("-" * 40)
        print("规划阶段执行完成!")
        
        if result is None:
            print("❌ 规划结果为空")
            return False, None
        
        print(f"结果类型: {type(result)}")
        
        if hasattr(result, 'competitors_to_research') and hasattr(result, 'sub_tasks'):
            competitors = result.competitors_to_research
            sub_tasks = result.sub_tasks
            
            print(f"✅ 识别竞品数量: {len(competitors)}")
            print(f"✅ 子任务数量: {len(sub_tasks)}")
            
            print("\n识别的竞品:")
            for i, competitor in enumerate(competitors, 1):
                print(f"  {i}. {competitor}")
            
            print("\n子任务:")
            for i, task in enumerate(sub_tasks, 1):
                task_name = task.task_name if hasattr(task, 'task_name') else task.get('task_name', 'Unknown')
                criteria = task.acceptance_criteria if hasattr(task, 'acceptance_criteria') else task.get('acceptance_criteria', 'Unknown')
                print(f"  {i}. {task_name}")
                print(f"     验收标准: {criteria}")
            
            return True, result
        else:
            print(f"❌ 结果格式异常: {result}")
            return False, None
        
    except KeyboardInterrupt:
        print("\n🛑 执行被用户中断")
        return True, None
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        print(f"错误类型: {type(e)}")
        import traceback
        traceback.print_exc()
        return False, None


def test_data_validation(planning_result):
    """验证规划结果的数据质量"""
    print("\n" + "=" * 60)
    print("验证规划结果数据质量")
    print("=" * 60)
    
    if planning_result is None:
        print("❌ 无规划结果可验证")
        return False
    
    try:
        # 验证竞品
        competitors = planning_result.competitors_to_research
        if not competitors or len(competitors) == 0:
            print("❌ 未识别到任何竞品")
            return False
        
        print(f"✅ 竞品数量合理: {len(competitors)}个")
        
        # 检查竞品名称质量
        valid_competitors = 0
        for competitor in competitors:
            if competitor and len(competitor.strip()) > 2:
                valid_competitors += 1
        
        if valid_competitors == len(competitors):
            print(f"✅ 所有竞品名称有效")
        else:
            print(f"⚠️  部分竞品名称可能无效: {valid_competitors}/{len(competitors)}")
        
        # 验证子任务
        sub_tasks = planning_result.sub_tasks
        if not sub_tasks or len(sub_tasks) == 0:
            print("❌ 未生成任何子任务")
            return False
        
        print(f"✅ 子任务数量合理: {len(sub_tasks)}个")
        
        # 检查子任务质量
        valid_tasks = 0
        for task in sub_tasks:
            task_name = task.task_name if hasattr(task, 'task_name') else task.get('task_name', '')
            criteria = task.acceptance_criteria if hasattr(task, 'acceptance_criteria') else task.get('acceptance_criteria', '')
            
            if task_name and len(task_name.strip()) > 3 and criteria and len(criteria.strip()) > 10:
                valid_tasks += 1
        
        if valid_tasks == len(sub_tasks):
            print(f"✅ 所有子任务质量良好")
        else:
            print(f"⚠️  部分子任务质量待改进: {valid_tasks}/{len(sub_tasks)}")
        
        # 检查是否与中医相关
        input_text = str(planning_result).lower()
        medical_keywords = ['中医', '医疗', '诊疗', '健康', '医院', '诊断', '治疗']
        found_keywords = [kw for kw in medical_keywords if kw in input_text]
        
        if found_keywords:
            print(f"✅ 结果与医疗领域相关，包含关键词: {found_keywords}")
        else:
            print("⚠️  结果可能与医疗领域相关性不强")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False


def test_performance():
    """测试性能指标"""
    print("\n" + "=" * 60)
    print("性能测试")
    print("=" * 60)
    
    import time
    
    test_input = {
        "initial_input": "中医辅助诊疗系统"
    }
    
    print("测试规划阶段响应时间...")
    
    try:
        start_time = time.time()
        result = planning_chain.invoke(test_input)
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"✅ 执行时间: {execution_time:.2f}秒")
        
        if execution_time < 30:
            print("✅ 响应时间良好 (<30秒)")
        elif execution_time < 60:
            print("⚠️  响应时间一般 (30-60秒)")
        else:
            print("❌ 响应时间较慢 (>60秒)")
        
        return execution_time < 120  # 2分钟内算合格
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("完整工作流测试 - 中医辅助诊疗系统")
    print("使用Python解释器: D:/ProgramData/Miniconda3/envs/llms/python.exe")
    
    tests = [
        ("规划阶段测试", test_planning_phase),
    ]
    
    results = []
    planning_result = None
    
    # 执行主要测试
    for test_name, test_func in tests:
        try:
            if test_name == "规划阶段测试":
                result, planning_result = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 如果规划成功，进行后续验证
    if planning_result is not None:
        try:
            validation_result = test_data_validation(planning_result)
            results.append(("数据质量验证", validation_result))
        except Exception as e:
            print(f"❌ 数据验证异常: {e}")
            results.append(("数据质量验证", False))
        
        try:
            performance_result = test_performance()
            results.append(("性能测试", performance_result))
        except Exception as e:
            print(f"❌ 性能测试异常: {e}")
            results.append(("性能测试", False))
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 完整工作流测试通过！")
        print("\n系统状态:")
        print("- ✅ Ollama模型连接正常")
        print("- ✅ 规划阶段功能正常")
        print("- ✅ 日志记录系统正常")
        print("- ✅ 结构化输出正常")
        print("- ✅ 数据质量良好")
        print("\n系统已准备就绪，可以正常使用！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n✅ 测试被用户中断 - Ctrl+C功能正常工作！")
        sys.exit(0)
