#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICA 增强版基础测试脚本
测试不依赖网络的增强版核心功能
"""

import os
import sys
from datetime import datetime

def test_enhanced_core_functionality():
    """测试增强版核心功能（不依赖网络）"""
    print("🔍 测试增强版核心功能...")
    
    try:
        from aica_agent.state import (
            AgentState, CompetitorInfo, SearchResult, 
            CompetitorResearch, DetailedContent
        )
        from aica_agent.utils.callbacks import AICACallbackHandler, PhaseCallbackManager
        from aica_agent.utils.logger import init_logger
        
        # 初始化日志系统
        logger = init_logger("test_logs")
        
        # 创建测试数据
        competitor_info = CompetitorInfo(
            name="微信小程序",
            company="腾讯",
            main_functions=["轻应用开发", "即用即走", "社交分享"],
            description="基于微信生态的轻量级应用平台",
            relevance_score=0.95
        )
        
        search_result = SearchResult(
            title="微信小程序开发指南",
            url="https://developers.weixin.qq.com",
            snippet="微信小程序是一种不需要下载安装即可使用的应用",
            relevance_score=0.9
        )
        
        detailed_content = DetailedContent(
            url="https://developers.weixin.qq.com",
            title="微信小程序开发指南",
            content="微信小程序开发的详细内容...",
            keywords=["微信小程序", "开发", "API"],
            summary="微信小程序开发的核心概念和API介绍"
        )
        
        competitor_research = CompetitorResearch(
            competitor_info=competitor_info,
            search_keywords=["微信小程序 功能", "微信小程序 商业模式", "微信小程序 用户评价"],
            collected_contents=[detailed_content],
            analysis_summary="微信小程序是腾讯推出的轻量级应用平台...",
            is_complete=True
        )
        
        # 创建增强版状态
        state = AgentState(
            initial_input="小程序开发平台",
            initial_search_results=[search_result],
            identified_competitors=[competitor_info],
            selected_competitors=[competitor_info],
            competitor_research=[competitor_research]
        )
        
        print(f"✅ 增强版核心功能测试成功")
        print(f"   - 竞品信息: {competitor_info.name} ({competitor_info.company})")
        print(f"   - 相关性评分: {competitor_info.relevance_score}")
        print(f"   - 搜索关键词数: {len(competitor_research.search_keywords)}")
        print(f"   - 收集内容数: {len(competitor_research.collected_contents)}")
        
        return True
    except Exception as e:
        print(f"❌ 增强版核心功能测试失败: {e}")
        return False

def test_callback_functionality():
    """测试Callback功能"""
    print("\n🔍 测试Callback功能...")
    
    try:
        from aica_agent.utils.callbacks import AICACallbackHandler
        from aica_agent.utils.logger import get_logger
        
        # 创建callback处理器
        callback = AICACallbackHandler("TEST")
        
        # 模拟LLM调用
        callback.on_llm_start(
            {"name": "test_llm"}, 
            ["测试提示词"]
        )
        
        # 模拟Chain调用
        callback.on_chain_start(
            {"name": "test_chain"},
            {"input": "测试输入"}
        )
        
        callback.on_chain_end({"output": "测试输出"})
        
        # 模拟工具调用
        callback.on_tool_start(
            {"name": "test_tool"},
            "测试工具输入"
        )
        
        callback.on_tool_end("测试工具输出")
        
        print("✅ Callback功能测试成功")
        return True
    except Exception as e:
        print(f"❌ Callback功能测试失败: {e}")
        return False

def test_chain_structures():
    """测试链结构（不调用LLM）"""
    print("\n🔍 测试链结构...")
    
    try:
        from aica_agent.chains import (
            competitor_identification_chain,
            competitor_selection_chain,
            keyword_generation_chain,
            content_relevance_chain,
            content_analysis_chain
        )
        
        # 检查链的基本属性
        chains_info = [
            ("竞品识别链", competitor_identification_chain),
            ("竞品选择链", competitor_selection_chain),
            ("关键词生成链", keyword_generation_chain),
            ("内容相关性链", content_relevance_chain),
            ("内容分析链", content_analysis_chain)
        ]
        
        for name, chain in chains_info:
            if hasattr(chain, 'steps') or hasattr(chain, 'first') or hasattr(chain, 'last'):
                print(f"✅ {name} 结构正确")
            else:
                print(f"❌ {name} 结构异常")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 链结构测试失败: {e}")
        return False

def test_enhanced_workflow_logic():
    """测试增强版工作流逻辑"""
    print("\n🔍 测试增强版工作流逻辑...")
    
    try:
        # 模拟工作流程
        print("  📋 模拟竞品确认阶段:")
        print("    1. 用户输入: '小程序开发平台'")
        print("    2. 搜索相关信息 (10条结果)")
        print("    3. 识别竞品信息")
        print("    4. 选择深度研究竞品 (3个)")
        
        print("  🔍 模拟详细研究阶段:")
        print("    1. 为每个竞品生成搜索关键词 (5个)")
        print("    2. 搜索并判断内容相关性")
        print("    3. 抓取高相关性网页内容")
        print("    4. 分析和总结收集的内容")
        
        print("  📄 模拟报告生成阶段:")
        print("    1. 整合竞品确认结果")
        print("    2. 整合详细研究结果")
        print("    3. 生成增强版分析报告")
        
        print("✅ 增强版工作流逻辑测试成功")
        return True
    except Exception as e:
        print(f"❌ 增强版工作流逻辑测试失败: {e}")
        return False

def test_configuration_validation():
    """测试配置验证"""
    print("\n🔍 测试配置验证...")
    
    try:
        from config import (
            INITIAL_SEARCH_RESULTS,
            SELECTED_COMPETITORS,
            KEYWORDS_PER_COMPETITOR,
            MAX_PAGES_PER_KEYWORD,
            CONTENT_ANALYSIS_THRESHOLD
        )
        
        # 验证配置合理性
        validations = [
            (INITIAL_SEARCH_RESULTS > 0, f"初始搜索结果数应大于0，当前: {INITIAL_SEARCH_RESULTS}"),
            (SELECTED_COMPETITORS > 0, f"选定竞品数应大于0，当前: {SELECTED_COMPETITORS}"),
            (KEYWORDS_PER_COMPETITOR > 0, f"每竞品关键词数应大于0，当前: {KEYWORDS_PER_COMPETITOR}"),
            (MAX_PAGES_PER_KEYWORD > 0, f"每关键词最大页面数应大于0，当前: {MAX_PAGES_PER_KEYWORD}"),
            (0 <= CONTENT_ANALYSIS_THRESHOLD <= 1, f"内容分析阈值应在0-1之间，当前: {CONTENT_ANALYSIS_THRESHOLD}"),
            (SELECTED_COMPETITORS <= INITIAL_SEARCH_RESULTS, f"选定竞品数不应超过搜索结果数")
        ]
        
        for is_valid, message in validations:
            if is_valid:
                print(f"✅ {message.split('，')[0]} 配置正确")
            else:
                print(f"❌ {message}")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        return False

def test_enhanced_features_summary():
    """测试增强功能总结"""
    print("\n🔍 增强功能总结...")
    
    features = [
        "✨ 竞品确认阶段 - 基于搜索的智能竞品识别",
        "🔍 详细研究阶段 - 多关键词深度信息收集",
        "🤖 LLM判断机制 - 智能内容相关性评估",
        "📊 结构化数据 - 完整的竞品信息模型",
        "🎯 可配置参数 - 灵活的搜索和分析参数",
        "📝 详细日志 - 完整的Callback日志记录",
        "🔄 增强工作流 - 优化的分析流程",
        "📄 增强报告 - 更详细的分析报告"
    ]
    
    print("🎉 AICA 增强版新功能:")
    for feature in features:
        print(f"  {feature}")
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 AICA 增强版基础测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有基础测试
    test_results.append(("增强版核心功能", test_enhanced_core_functionality()))
    test_results.append(("Callback功能", test_callback_functionality()))
    test_results.append(("链结构", test_chain_structures()))
    test_results.append(("工作流逻辑", test_enhanced_workflow_logic()))
    test_results.append(("配置验证", test_configuration_validation()))
    test_results.append(("功能总结", test_enhanced_features_summary()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 增强版基础测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个基础测试通过")
    
    if passed == total:
        print("🎉 所有增强版基础测试通过！核心功能正常。")
        print("\n🆕 增强版主要改进:")
        print("1. 🔍 竞品确认阶段 - 搜索10条数据，智能识别竞品")
        print("2. 📊 详细研究阶段 - 为每个竞品生成5个关键词深度搜寻")
        print("3. 🤖 LLM判断机制 - 智能评估内容相关性")
        print("4. 📝 Callback日志 - 每个chain都有详细的处理日志")
        print("5. ⚙️ 参数配置化 - 搜索数量、竞品数量等可配置")
        print("\n📝 下一步:")
        print("1. 安装网络依赖: pip install ddgs langgraph langchain-community")
        print("2. 运行完整测试: python test_enhanced.py")
        print("3. 启动增强版系统: python main.py")
        return True
    else:
        print("⚠️  部分增强版基础测试失败，请检查系统配置。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
